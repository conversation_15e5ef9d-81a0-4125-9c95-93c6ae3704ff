package com.example.springsecurity.config;

import com.example.springsecurity.entity.SysPermission;
import com.example.springsecurity.entity.SysRole;
import com.example.springsecurity.entity.SysUser;
import com.example.springsecurity.repository.SysPermissionRepository;
import com.example.springsecurity.repository.SysRoleRepository;
import com.example.springsecurity.repository.SysUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 数据初始化器
 * 
 * <AUTHOR> Security Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final SysUserRepository userRepository;
    private final SysRoleRepository roleRepository;
    private final SysPermissionRepository permissionRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        log.info("Starting data initialization...");
        
        initPermissions();
        initRoles();
        initUsers();
        
        log.info("Data initialization completed.");
    }

    /**
     * 初始化权限数据
     */
    private void initPermissions() {
        if (permissionRepository.count() > 0) {
            log.info("Permissions already exist, skipping initialization.");
            return;
        }

        log.info("Initializing permissions...");

        // 用户管理权限
        createPermission("user:read", "查看用户", "API", "/api/users/**", "GET");
        createPermission("user:create", "创建用户", "API", "/api/users", "POST");
        createPermission("user:update", "更新用户", "API", "/api/users/**", "PUT");
        createPermission("user:delete", "删除用户", "API", "/api/users/**", "DELETE");
        createPermission("user:reset-password", "重置密码", "API", "/api/users/*/reset-password", "PUT");
        createPermission("user:update-status", "更新用户状态", "API", "/api/users/*/status", "PUT");

        // 角色管理权限
        createPermission("role:read", "查看角色", "API", "/api/roles/**", "GET");
        createPermission("role:create", "创建角色", "API", "/api/roles", "POST");
        createPermission("role:update", "更新角色", "API", "/api/roles/**", "PUT");
        createPermission("role:delete", "删除角色", "API", "/api/roles/**", "DELETE");

        // 权限管理权限
        createPermission("permission:read", "查看权限", "API", "/api/permissions/**", "GET");
        createPermission("permission:create", "创建权限", "API", "/api/permissions", "POST");
        createPermission("permission:update", "更新权限", "API", "/api/permissions/**", "PUT");
        createPermission("permission:delete", "删除权限", "API", "/api/permissions/**", "DELETE");

        log.info("Permissions initialization completed.");
    }

    /**
     * 初始化角色数据
     */
    private void initRoles() {
        if (roleRepository.count() > 0) {
            log.info("Roles already exist, skipping initialization.");
            return;
        }

        log.info("Initializing roles...");

        // 创建管理员角色
        SysRole adminRole = createRole("ADMIN", "管理员", "系统管理员，拥有所有权限");
        Set<SysPermission> adminPermissions = Set.copyOf(permissionRepository.findAll());
        adminRole.setPermissions(adminPermissions);
        roleRepository.save(adminRole);

        // 创建普通用户角色
        SysRole userRole = createRole("USER", "普通用户", "普通用户，拥有基本权限");
        Set<SysPermission> userPermissions = Set.of(
            permissionRepository.findByPermissionCode("user:read").orElse(null)
        );
        userPermissions.removeIf(java.util.Objects::isNull);
        userRole.setPermissions(userPermissions);
        roleRepository.save(userRole);

        log.info("Roles initialization completed.");
    }

    /**
     * 初始化用户数据
     */
    private void initUsers() {
        if (userRepository.count() > 0) {
            log.info("Users already exist, skipping initialization.");
            return;
        }

        log.info("Initializing users...");

        // 创建管理员用户
        SysRole adminRole = roleRepository.findByRoleCode("ADMIN").orElse(null);
        if (adminRole != null) {
            SysUser admin = new SysUser();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setEmail("<EMAIL>");
            admin.setRealName("系统管理员");
            admin.setStatus(1);
            admin.setRoles(Set.of(adminRole));
            userRepository.save(admin);
            log.info("Admin user created: admin/admin123");
        }

        // 创建测试用户
        SysRole userRole = roleRepository.findByRoleCode("USER").orElse(null);
        if (userRole != null) {
            SysUser testUser = new SysUser();
            testUser.setUsername("testuser");
            testUser.setPassword(passwordEncoder.encode("test123"));
            testUser.setEmail("<EMAIL>");
            testUser.setRealName("测试用户");
            testUser.setStatus(1);
            testUser.setRoles(Set.of(userRole));
            userRepository.save(testUser);
            log.info("Test user created: testuser/test123");
        }

        log.info("Users initialization completed.");
    }

    /**
     * 创建权限
     */
    private SysPermission createPermission(String code, String name, String type, String url, String method) {
        SysPermission permission = new SysPermission();
        permission.setPermissionCode(code);
        permission.setPermissionName(name);
        permission.setResourceType(type);
        permission.setUrl(url);
        permission.setMethod(method);
        permission.setStatus(1);
        return permissionRepository.save(permission);
    }

    /**
     * 创建角色
     */
    private SysRole createRole(String code, String name, String description) {
        SysRole role = new SysRole();
        role.setRoleCode(code);
        role.setRoleName(name);
        role.setDescription(description);
        role.setStatus(1);
        return roleRepository.save(role);
    }
}
