package com.example.springsecurity.controller;

import com.example.springsecurity.dto.LoginRequest;
import com.example.springsecurity.dto.LoginResponse;
import com.example.springsecurity.dto.RegisterRequest;
import com.example.springsecurity.dto.Result;
import com.example.springsecurity.entity.SysUser;
import com.example.springsecurity.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR> Security Team
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse response = authService.login(loginRequest);
            return Result.success("登录成功", response);
        } catch (Exception e) {
            log.error("Login failed for user: {}", loginRequest.getUsername(), e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册响应
     */
    @PostMapping("/register")
    public Result<String> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            SysUser user = authService.register(registerRequest);
            return Result.success("注册成功", "用户ID: " + user.getId());
        } catch (Exception e) {
            log.error("Registration failed for user: {}", registerRequest.getUsername(), e);
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     * @return 登出响应
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                log.info("User logout: {}", authentication.getName());
                SecurityContextHolder.clearContext();
            }
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("Logout failed", e);
            return Result.error("登出失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    @GetMapping("/me")
    public Result<Object> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                return Result.success("获取用户信息成功", authentication.getPrincipal());
            }
            return Result.unauthorized("用户未登录");
        } catch (Exception e) {
            log.error("Get current user failed", e);
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 刷新Token
     * @return 新的Token
     */
    @PostMapping("/refresh")
    public Result<String> refreshToken() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                // 这里可以实现Token刷新逻辑
                // 目前简单返回成功消息
                return Result.success("Token刷新成功");
            }
            return Result.unauthorized("用户未登录");
        } catch (Exception e) {
            log.error("Refresh token failed", e);
            return Result.error("Token刷新失败：" + e.getMessage());
        }
    }
}
