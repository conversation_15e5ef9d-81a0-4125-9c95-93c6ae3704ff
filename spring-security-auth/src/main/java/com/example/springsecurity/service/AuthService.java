package com.example.springsecurity.service;

import com.example.springsecurity.dto.LoginRequest;
import com.example.springsecurity.dto.LoginResponse;
import com.example.springsecurity.dto.RegisterRequest;
import com.example.springsecurity.entity.SysRole;
import com.example.springsecurity.entity.SysUser;
import com.example.springsecurity.repository.SysRoleRepository;
import com.example.springsecurity.repository.SysUserRepository;
import com.example.springsecurity.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证服务
 * 
 * <AUTHOR> Security Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;
    private final PasswordEncoder passwordEncoder;
    private final SysUserRepository userRepository;
    private final SysRoleRepository roleRepository;

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @Transactional(readOnly = true)
    public LoginResponse login(LoginRequest loginRequest) {
        log.info("User login attempt: {}", loginRequest.getUsername());
        
        // 认证用户
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        // 生成JWT Token
        String token = tokenProvider.generateToken(authentication);
        Long expiresIn = tokenProvider.getExpirationTime();

        // 获取用户信息
        SysUser user = (SysUser) authentication.getPrincipal();
        LoginResponse.UserInfo userInfo = buildUserInfo(user);

        log.info("User login successful: {}", loginRequest.getUsername());
        return new LoginResponse(token, expiresIn, userInfo);
    }

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 用户信息
     */
    @Transactional
    public SysUser register(RegisterRequest registerRequest) {
        log.info("User registration attempt: {}", registerRequest.getUsername());
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerRequest.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (registerRequest.getEmail() != null && userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        // 检查手机号是否已存在
        if (registerRequest.getPhone() != null && userRepository.existsByPhone(registerRequest.getPhone())) {
            throw new RuntimeException("手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());
        user.setPhone(registerRequest.getPhone());
        user.setRealName(registerRequest.getRealName());
        user.setStatus(1);

        // 分配默认角色（USER）
        SysRole userRole = roleRepository.findByRoleCode("USER")
                .orElseThrow(() -> new RuntimeException("默认角色不存在"));
        user.setRoles(Set.of(userRole));

        SysUser savedUser = userRepository.save(user);
        log.info("User registration successful: {}", registerRequest.getUsername());
        
        return savedUser;
    }

    /**
     * 构建用户信息
     * @param user 用户实体
     * @return 用户信息DTO
     */
    private LoginResponse.UserInfo buildUserInfo(SysUser user) {
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setEmail(user.getEmail());
        userInfo.setPhone(user.getPhone());
        userInfo.setRealName(user.getRealName());
        userInfo.setAvatar(user.getAvatar());

        // 获取角色列表
        List<String> roles = user.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .filter(authority -> authority.startsWith("ROLE_"))
                .map(authority -> authority.substring(5)) // 移除 "ROLE_" 前缀
                .collect(Collectors.toList());
        userInfo.setRoles(roles);

        // 获取权限列表
        List<String> permissions = user.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .filter(authority -> !authority.startsWith("ROLE_"))
                .collect(Collectors.toList());
        userInfo.setPermissions(permissions);

        return userInfo;
    }
}
