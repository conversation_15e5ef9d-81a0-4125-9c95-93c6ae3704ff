package com.example.springsecurity.service;

import com.example.springsecurity.entity.SysUser;
import com.example.springsecurity.repository.SysUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 用户服务
 * 
 * <AUTHOR> Security Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final SysUserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 分页查询用户
     * @param pageable 分页参数
     * @return 用户分页数据
     */
    @Transactional(readOnly = true)
    public Page<SysUser> findUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public Optional<SysUser> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public Optional<SysUser> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 创建用户
     * @param user 用户信息
     * @return 创建的用户
     */
    @Transactional
    public SysUser createUser(SysUser user) {
        log.info("Creating user: {}", user.getUsername());
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        return userRepository.save(user);
    }

    /**
     * 更新用户
     * @param user 用户信息
     * @return 更新的用户
     */
    @Transactional
    public SysUser updateUser(SysUser user) {
        log.info("Updating user: {}", user.getUsername());
        
        SysUser existingUser = userRepository.findById(user.getId())
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 更新用户信息（不包括密码）
        existingUser.setEmail(user.getEmail());
        existingUser.setPhone(user.getPhone());
        existingUser.setRealName(user.getRealName());
        existingUser.setAvatar(user.getAvatar());
        existingUser.setStatus(user.getStatus());

        return userRepository.save(existingUser);
    }

    /**
     * 修改密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        log.info("Changing password for user: {}", userId);
        
        SysUser user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("旧密码不正确");
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    /**
     * 重置密码
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    @Transactional
    public void resetPassword(Long userId, String newPassword) {
        log.info("Resetting password for user: {}", userId);
        
        SysUser user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    /**
     * 启用/禁用用户
     * @param userId 用户ID
     * @param status 状态（0-禁用，1-启用）
     */
    @Transactional
    public void updateUserStatus(Long userId, Integer status) {
        log.info("Updating user status: {} to {}", userId, status);
        
        SysUser user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setStatus(status);
        userRepository.save(user);
    }

    /**
     * 删除用户
     * @param userId 用户ID
     */
    @Transactional
    public void deleteUser(Long userId) {
        log.info("Deleting user: {}", userId);
        
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("用户不存在");
        }

        userRepository.deleteById(userId);
    }
}
