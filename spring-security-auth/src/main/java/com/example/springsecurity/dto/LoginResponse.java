package com.example.springsecurity.dto;

import lombok.Data;

import java.util.List;

/**
 * 登录响应DTO
 * 
 * <AUTHOR> Security Team
 */
@Data
public class LoginResponse {

    private String token;
    private String tokenType = "Bearer";
    private Long expiresIn;
    private UserInfo userInfo;

    @Data
    public static class UserInfo {
        private Long id;
        private String username;
        private String email;
        private String phone;
        private String realName;
        private String avatar;
        private List<String> roles;
        private List<String> permissions;
    }

    public LoginResponse(String token, Long expiresIn, UserInfo userInfo) {
        this.token = token;
        this.expiresIn = expiresIn;
        this.userInfo = userInfo;
    }
}
