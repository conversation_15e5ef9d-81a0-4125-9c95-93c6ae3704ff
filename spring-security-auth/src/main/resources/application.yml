server:
  port: 8081
  servlet:
    context-path: /spring-security-auth

spring:
  application:
    name: spring-security-auth
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# JWT配置
jwt:
  secret: mySecretKeyForSpringSecurityJWTTokenGenerationThatIsLongEnoughForHS512Algorithm
  expiration: 86400000  # 24小时，单位毫秒
  header: Authorization
  prefix: Bearer 

# 日志配置
logging:
  level:
    com.example.springsecurity: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/spring-security-auth.log
