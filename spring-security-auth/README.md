# Spring Security 权限控制项目

## 项目简介

本项目是一个基于 Spring Security + JWT 的用户权限控制系统，实现了完整的 RBAC（基于角色的访问控制）权限模型。

## 技术栈

- **Spring Boot 3.1.0** - 主框架
- **Spring Security** - 安全框架
- **JWT (JSON Web Token)** - 无状态认证
- **Spring Data JPA** - 数据访问层
- **MySQL 8.0** - 数据库
- **Lombok** - 简化代码
- **Maven** - 项目构建

## 功能特性

### 认证功能
- ✅ 用户注册
- ✅ 用户登录
- ✅ JWT Token 生成和验证
- ✅ 用户登出
- ✅ 获取当前用户信息

### 权限控制
- ✅ 基于角色的访问控制 (RBAC)
- ✅ 方法级权限控制 (@PreAuthorize)
- ✅ URL 级权限控制
- ✅ 动态权限验证

### 用户管理
- ✅ 用户 CRUD 操作
- ✅ 密码修改和重置
- ✅ 用户状态管理（启用/禁用）
- ✅ 分页查询

## 数据库设计

### 核心表结构
1. **sys_user** - 用户表
2. **sys_role** - 角色表
3. **sys_permission** - 权限表
4. **sys_user_role** - 用户角色关联表
5. **sys_role_permission** - 角色权限关联表

## 快速开始

### 1. 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置
```sql
CREATE DATABASE spring_security_auth CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 修改配置文件
编辑 `src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ************************************************
    username: your_username
    password: your_password
```

### 4. 启动项目
```bash
mvn spring-boot:run
```

### 5. 访问地址
- 应用地址：http://localhost:8081/spring-security-auth
- 数据库表会自动创建，初始数据会自动插入

## 默认账号

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | ADMIN | 管理员账号，拥有所有权限 |
| testuser | test123 | USER | 测试账号，拥有基本权限 |

## API 接口

### 认证接口
```http
POST /auth/login          # 用户登录
POST /auth/register       # 用户注册
POST /auth/logout         # 用户登出
GET  /auth/me            # 获取当前用户信息
POST /auth/refresh       # 刷新Token
```

### 用户管理接口
```http
GET    /api/users              # 查询用户列表（需要权限）
GET    /api/users/{id}         # 查询用户详情
POST   /api/users              # 创建用户（需要权限）
PUT    /api/users/{id}         # 更新用户
PUT    /api/users/{id}/password # 修改密码
PUT    /api/users/{id}/reset-password # 重置密码（管理员）
PUT    /api/users/{id}/status  # 更新用户状态（管理员）
DELETE /api/users/{id}         # 删除用户（管理员）
```

## 使用示例

### 1. 用户登录
```bash
curl -X POST http://localhost:8081/spring-security-auth/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 访问受保护的接口
```bash
curl -X GET http://localhost:8081/spring-security-auth/api/users \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 权限配置

### 角色权限
- **ADMIN**: 拥有所有权限
- **USER**: 拥有基本查看权限

### 权限编码
- `user:read` - 查看用户
- `user:create` - 创建用户
- `user:update` - 更新用户
- `user:delete` - 删除用户
- `user:reset-password` - 重置密码
- `user:update-status` - 更新用户状态

## 安全特性

1. **密码加密**: 使用 BCrypt 加密存储
2. **JWT 安全**: 使用 HS512 算法签名
3. **会话管理**: 无状态设计，支持分布式部署
4. **异常处理**: 统一的安全异常处理
5. **CORS 支持**: 支持跨域请求

## 项目结构

```
src/main/java/com/example/springsecurity/
├── config/          # 配置类
├── controller/      # 控制器
├── dto/            # 数据传输对象
├── entity/         # 实体类
├── repository/     # 数据访问层
├── security/       # 安全相关
└── service/        # 业务逻辑层
```

## 开发说明

### 添加新的权限
1. 在 `DataInitializer` 中添加权限定义
2. 在对应的 Controller 方法上添加 `@PreAuthorize` 注解
3. 重启应用，权限会自动初始化

### 自定义 JWT 配置
修改 `application.yml` 中的 JWT 相关配置：
```yaml
jwt:
  secret: your-secret-key
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer 
```

## 注意事项

1. JWT Secret 在生产环境中应该使用更复杂的密钥
2. 数据库连接信息不要提交到版本控制系统
3. 建议在生产环境中启用 HTTPS
4. 定期更新依赖版本以修复安全漏洞
