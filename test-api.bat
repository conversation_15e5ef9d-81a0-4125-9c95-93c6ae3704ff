@echo off
chcp 65001 >nul
echo ========================================
echo 测试权限控制项目API接口
echo ========================================
echo.

echo 1. 测试Spring Security项目登录
echo POST http://localhost:8081/spring-security-auth/auth/login
curl -X POST http://localhost:8081/spring-security-auth/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"admin\",\"password\":\"admin123\"}"
echo.
echo.

echo 2. 测试Sa-Token项目登录
echo POST http://localhost:8082/sa-token-auth/auth/login
curl -X POST http://localhost:8082/sa-token-auth/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"admin\",\"password\":\"admin123\"}"
echo.
echo.

echo ========================================
echo 说明：
echo - 两个项目都正常运行
echo - 根路径需要认证是正常的安全行为
echo - 登录接口使用POST方法，不是GET
echo - 登录成功后会返回JWT Token
echo ========================================
pause
