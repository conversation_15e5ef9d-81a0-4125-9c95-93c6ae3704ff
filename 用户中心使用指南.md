# 用户中心使用指南

## 概述
用户中心是一个基于 Spring Boot 的微服务，提供用户注册、登录、认证等功能。采用 API 分离设计，支持 JWT 认证和 OpenAPI 文档。

## 环境要求
- **JDK**: 17+
- **Spring Boot**: 3.1.0
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **端口**: 8084 (默认)

## 快速启动

### 1. 环境准备
```bash
# 确保 MySQL 服务运行
# 创建数据库
CREATE DATABASE user_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 配置数据库
编辑 `user-center/src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: ****************************************************************************************************
    username: root
    password: 123456  # 修改为你的密码
```

### 3. 编译项目
```bash
# 在项目根目录执行
mvn clean install -pl user-center-api,user-center
```

### 4. 启动服务
```bash
# 方式1: 使用 Maven
cd user-center
mvn spring-boot:run

# 方式2: 直接运行主类
java -jar target/user-center-1.0-SNAPSHOT.jar

# 方式3: 在 IDE 中运行 UserCenterApplication.main()
```

### 5. 验证启动
```bash
# 检查服务状态
curl http://localhost:8084/actuator/health

# 访问 API 文档
http://localhost:8084/swagger-ui/index.html
```

## API 接口说明

### 基础信息
- **Base URL**: `http://localhost:8084`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token (除注册和登录外)

### 1. 用户注册
```bash
POST /api/users/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "nickname": "测试用户",
  "email": "<EMAIL>"
}
```

**响应示例**:
```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "createdAt": "2025-06-11T10:30:00"
  }
}
```

### 2. 用户登录
```bash
POST /api/users/login
Content-Type: application/x-www-form-urlencoded

username=testuser&password=123456
```

**响应示例**:
```json
{
  "success": true,
  "code": "SUCCESS", 
  "message": "操作成功",
  "data": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYyMzQwNjQwMCwiZXhwIjoxNjIzNDkyODAwfQ.xxx"
}
```

### 3. Token 验证
```bash
GET /api/users/validate
Authorization: Bearer <your-jwt-token>
```

### 4. 获取用户信息
```bash
GET /api/users/{id}
Authorization: Bearer <your-jwt-token>
```

### 5. 更新用户信息
```bash
PUT /api/users/{id}
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "username": "newusername",
  "nickname": "新昵称",
  "email": "<EMAIL>"
}
```

### 6. 删除用户
```bash
DELETE /api/users/{id}
Authorization: Bearer <your-jwt-token>
```

## 客户端集成

### 1. Feign 客户端使用
在其他微服务中使用 Feign 客户端：

```java
@FeignClient(
    name = "user-center",
    url = "${user-center.url:http://localhost:8084}"
)
public interface UserClient extends UserApi {
    // 继承 UserApi 接口即可
}
```

### 2. 配置文件
```yaml
user-center:
  url: http://localhost:8084
```

### 3. 使用示例
```java
@Service
public class BusinessService {
    @Autowired
    private UserClient userClient;
    
    public void doSomething() {
        // 验证用户token
        Result<UserDTO> result = userClient.validateToken("Bearer " + token);
        if (result.isSuccess()) {
            UserDTO user = result.getData();
            // 业务逻辑
        }
    }
}
```

## 安全配置

### 1. 公开接口
以下接口无需认证：
- `/api/users/register` - 用户注册
- `/api/users/login` - 用户登录  
- `/swagger-ui/**` - API 文档
- `/v3/api-docs/**` - OpenAPI 规范

### 2. JWT 配置
```yaml
jwt:
  secret: your-secret-key-here  # 修改为安全的密钥
  expiration: 86400000  # 24小时
```

## 数据库表结构

### users 表
```sql
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    email VARCHAR(100),
    avatar VARCHAR(255),
    created_at DATETIME,
    updated_at DATETIME
);
```

### roles 表
```sql
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(200),
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### user_roles 表
```sql
CREATE TABLE user_roles (
    user_id BIGINT,
    role_id BIGINT,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);
```

## 监控和日志

### 1. 健康检查
```bash
curl http://localhost:8084/actuator/health
```

### 2. 日志配置
日志文件位置: `logs/user-center/`
- 支持按日期滚动
- 最大保留30天
- 单文件最大100MB

### 3. 日志级别
```yaml
logging:
  level:
    root: INFO
    com.example: DEBUG
```

## 常见问题

### 1. 数据库连接失败
- 检查 MySQL 服务是否启动
- 验证数据库连接配置
- 确认数据库用户权限

### 2. JWT Token 无效
- 检查 token 是否过期
- 验证 JWT 密钥配置
- 确认 Authorization 头格式: `Bearer <token>`

### 3. 端口冲突
- 修改 `application.yml` 中的 `server.port`
- 或使用启动参数: `--server.port=8085`

## 开发建议

### 1. 本地开发
```bash
# 使用开发配置
mvn spring-boot:run -Dspring.profiles.active=dev
```

### 2. 生产部署
```bash
# 使用生产配置
java -jar user-center.jar --spring.profiles.active=prod
```

### 3. Docker 部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/user-center-1.0-SNAPSHOT.jar app.jar
EXPOSE 8084
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 快速启动工具

### 1. 自动启动脚本
```bash
# Linux/Mac
./user-center/start.sh

# Windows
user-center\start.bat
```

### 2. API 测试脚本
```bash
# Linux/Mac
./user-center/test-scripts/api-test.sh

# Windows
user-center\test-scripts\api-test.bat
```

### 3. Postman 集合
导入文件: `user-center/postman/User-Center-API.postman_collection.json`

## API 文档访问
启动服务后，访问以下地址查看完整的 API 文档：
- Swagger UI: http://localhost:8084/swagger-ui/index.html
- OpenAPI JSON: http://localhost:8084/v3/api-docs

## 文件结构
```
user-center/
├── src/main/java/com/example/user/
│   ├── UserCenterApplication.java    # 启动类
│   ├── controller/                   # 控制器
│   ├── service/                      # 服务层
│   ├── repository/                   # 数据访问层
│   ├── entity/                       # 实体类
│   ├── config/                       # 配置类
│   └── exception/                    # 异常处理
├── src/main/resources/
│   └── application.yml               # 配置文件
├── start.sh / start.bat             # 启动脚本
├── test-scripts/                    # 测试脚本
│   ├── api-test.sh
│   └── api-test.bat
└── postman/                         # Postman 集合
    └── User-Center-API.postman_collection.json
```
