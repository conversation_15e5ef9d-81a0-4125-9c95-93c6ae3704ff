# 用户权限控制项目实施任务

## 任务概述
创建两个独立的用户权限控制项目：
1. spring-security-auth：使用Spring Security + JWT
2. sa-token-auth：使用Sa-Token + JWT

## 项目需求
- 独立的Spring Boot应用
- 完整的用户注册、登录、权限验证功能
- 集成数据库（用户表、角色表、权限表）
- 提供RESTful API接口
- 标准RBAC权限模型

## 数据库设计
### 表结构
1. sys_user：用户表
2. sys_role：角色表  
3. sys_permission：权限表
4. sys_user_role：用户角色关联表
5. sys_role_permission：角色权限关联表

## 实施计划
1. ✅ 创建项目基础结构
2. ⏳ 设计数据库表结构
3. ⏳ 实现实体类和数据访问层
4. ⏳ 实现Spring Security项目
5. ⏳ 实现Sa-Token项目
6. ⏳ 实现业务逻辑和API
7. ⏳ 创建测试数据
8. ⏳ 编写测试用例

## 当前状态
正在执行步骤1：创建项目基础结构

## 备注
- 使用Java 17和Spring Boot 3.1.0
- 数据库使用MySQL 8.0
- 统一使用Maven构建工具
