@echo off
chcp 65001 >nul
echo.
echo 🧵 Java多线程编程学习项目演示脚本
echo =====================================
echo.

:menu
echo 请选择要运行的演示：
echo.
echo 1. 基础线程操作演示
echo 2. 同步机制演示
echo 3. 线程池演示
echo 4. 并发工具类演示
echo 5. 原子操作演示
echo 6. 并发集合演示
echo 7. CompletableFuture异步编程演示
echo 8. Fork/Join框架演示
echo 9. 生产者消费者模式演示
echo 10. 性能基准测试
echo 11. 启动Web应用
echo 12. 运行所有演示
echo 0. 退出
echo.
set /p choice=请输入选择 (0-12): 

if "%choice%"=="1" goto demo1
if "%choice%"=="2" goto demo2
if "%choice%"=="3" goto demo3
if "%choice%"=="4" goto demo4
if "%choice%"=="5" goto demo5
if "%choice%"=="6" goto demo6
if "%choice%"=="7" goto demo7
if "%choice%"=="8" goto demo8
if "%choice%"=="9" goto demo9
if "%choice%"=="10" goto demo10
if "%choice%"=="11" goto webapp
if "%choice%"=="12" goto all
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:demo1
echo.
echo 🔄 运行基础线程操作演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.basic.ThreadCreationDemo
pause
goto menu

:demo2
echo.
echo 🔒 运行同步机制演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.synchronization.SynchronizedDemo
pause
goto menu

:demo3
echo.
echo 🏊 运行线程池演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.threadpool.ThreadPoolDemo
pause
goto menu

:demo4
echo.
echo 🛠️ 运行并发工具类演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.concurrent.ConcurrentUtilsDemo
pause
goto menu

:demo5
echo.
echo ⚛️ 运行原子操作演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.atomic.AtomicDemo
pause
goto menu

:demo6
echo.
echo 📚 运行并发集合演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.collections.ConcurrentCollectionsDemo
pause
goto menu

:demo7
echo.
echo 🚀 运行CompletableFuture异步编程演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.async.CompletableFutureDemo
pause
goto menu

:demo8
echo.
echo 🍴 运行Fork/Join框架演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.forkjoin.ForkJoinDemo
pause
goto menu

:demo9
echo.
echo 🏭 运行生产者消费者模式演示...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.patterns.ProducerConsumerDemo
pause
goto menu

:demo10
echo.
echo 📊 运行性能基准测试...
java -cp "target/classes;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-classic\1.4.7\logback-classic-1.4.7.jar;%USERPROFILE%\.m2\repository\ch\qos\logback\logback-core\1.4.7\logback-core-1.4.7.jar;%USERPROFILE%\.m2\repository\org\slf4j\slf4j-api\2.0.7\slf4j-api-2.0.7.jar" com.example.multithreading.benchmark.SimpleBenchmark
pause
goto menu

:webapp
echo.
echo 🌐 启动Web应用...
echo 应用将在 http://localhost:8090 启动
echo 可以访问以下API接口：
echo   - http://localhost:8090/api/multithreading/demos
echo   - http://localhost:8090/api/multithreading/async/completable-future
echo   - http://localhost:8090/api/multithreading/performance/benchmark
echo.
echo 按 Ctrl+C 停止应用
cd ..
mvn spring-boot:run -pl multithreading-learning
goto menu

:all
echo.
echo 🎯 运行所有演示（按顺序执行）...
echo.
call :demo1
call :demo2
call :demo7
call :demo9
call :demo10
echo.
echo ✅ 所有演示运行完成！
pause
goto menu

:exit
echo.
echo 👋 感谢使用Java多线程编程学习项目！
echo.
exit /b 0
