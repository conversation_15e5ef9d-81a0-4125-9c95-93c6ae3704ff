package com.example.multithreading.concurrent;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并发工具类演示
 * 
 * 知识点：
 * 1. CountDownLatch - 倒计时门闩
 * 2. CyclicBarrier - 循环屏障
 * 3. Semaphore - 信号量
 * 4. Exchanger - 交换器
 * 5. Phaser - 阶段器
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ConcurrentUtilsDemo {

    public static void main(String[] args) throws InterruptedException {
        log.info("=== 并发工具类演示 ===");
        
        // 1. CountDownLatch演示
        demonstrateCountDownLatch();
        
        // 2. CyclicBarrier演示
        demonstrateCyclicBarrier();
        
        // 3. Semaphore演示
        demonstrateSemaphore();
        
        // 4. Exchanger演示
        demonstrateExchanger();
        
        // 5. Phaser演示
        demonstratePhaser();
    }

    /**
     * CountDownLatch演示 - 倒计时门闩
     * 用途：等待多个线程完成后再继续执行
     */
    private static void demonstrateCountDownLatch() throws InterruptedException {
        log.info("--- 1. CountDownLatch演示 ---");
        
        int workerCount = 3;
        CountDownLatch latch = new CountDownLatch(workerCount);
        
        // 创建工作线程
        for (int i = 1; i <= workerCount; i++) {
            final int workerId = i;
            new Thread(() -> {
                try {
                    log.info("工作线程 {} 开始工作", workerId);
                    Thread.sleep(1000 + workerId * 500); // 模拟不同的工作时间
                    log.info("工作线程 {} 完成工作", workerId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown(); // 完成工作，计数减1
                }
            }).start();
        }
        
        log.info("主线程等待所有工作线程完成...");
        latch.await(); // 等待计数归零
        log.info("所有工作线程已完成，主线程继续执行");
        
        log.info("CountDownLatch演示完成\n");
    }

    /**
     * CyclicBarrier演示 - 循环屏障
     * 用途：让一组线程到达屏障点时被阻塞，直到最后一个线程到达
     */
    private static void demonstrateCyclicBarrier() throws InterruptedException {
        log.info("--- 2. CyclicBarrier演示 ---");
        
        int participantCount = 3;
        CyclicBarrier barrier = new CyclicBarrier(participantCount, () -> {
            log.info("🎉 所有参与者都到达屏障点，开始下一阶段！");
        });
        
        // 创建参与者线程
        Thread[] participants = new Thread[participantCount];
        for (int i = 1; i <= participantCount; i++) {
            final int participantId = i;
            participants[i-1] = new Thread(() -> {
                try {
                    // 第一阶段
                    log.info("参与者 {} 完成第一阶段工作", participantId);
                    Thread.sleep(1000 + participantId * 300);
                    log.info("参与者 {} 到达第一个屏障点", participantId);
                    barrier.await();
                    
                    // 第二阶段
                    log.info("参与者 {} 开始第二阶段工作", participantId);
                    Thread.sleep(800 + participantId * 200);
                    log.info("参与者 {} 完成第二阶段工作", participantId);
                    barrier.await();
                    
                    log.info("参与者 {} 全部工作完成", participantId);
                    
                } catch (InterruptedException | BrokenBarrierException e) {
                    Thread.currentThread().interrupt();
                    log.error("参与者 {} 被中断", participantId);
                }
            });
        }
        
        // 启动所有参与者
        for (Thread participant : participants) {
            participant.start();
        }
        
        // 等待所有参与者完成
        for (Thread participant : participants) {
            participant.join();
        }
        
        log.info("CyclicBarrier演示完成\n");
    }

    /**
     * Semaphore演示 - 信号量
     * 用途：控制同时访问特定资源的线程数量
     */
    private static void demonstrateSemaphore() throws InterruptedException {
        log.info("--- 3. Semaphore演示 ---");
        
        // 模拟停车场，只有3个停车位
        int parkingSpots = 3;
        Semaphore semaphore = new Semaphore(parkingSpots);
        
        // 创建多辆车尝试停车
        Thread[] cars = new Thread[6];
        for (int i = 1; i <= cars.length; i++) {
            final int carId = i;
            cars[i-1] = new Thread(() -> {
                try {
                    log.info("车辆 {} 尝试进入停车场", carId);
                    semaphore.acquire(); // 获取停车位
                    
                    log.info("车辆 {} 成功停车，剩余停车位: {}", carId, semaphore.availablePermits());
                    Thread.sleep(2000 + carId * 300); // 停车时间
                    
                    log.info("车辆 {} 离开停车场", carId);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    semaphore.release(); // 释放停车位
                }
            });
        }
        
        // 启动所有车辆
        for (Thread car : cars) {
            car.start();
        }
        
        // 等待所有车辆完成
        for (Thread car : cars) {
            car.join();
        }
        
        log.info("Semaphore演示完成\n");
    }

    /**
     * Exchanger演示 - 交换器
     * 用途：两个线程之间交换数据
     */
    private static void demonstrateExchanger() throws InterruptedException {
        log.info("--- 4. Exchanger演示 ---");
        
        Exchanger<String> exchanger = new Exchanger<>();
        
        // 生产者线程
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 3; i++) {
                    String product = "产品-" + i;
                    log.info("生产者生产: {}", product);
                    
                    // 与消费者交换数据
                    String received = exchanger.exchange(product);
                    log.info("生产者收到消费者的反馈: {}", received);
                    
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        // 消费者线程
        Thread consumer = new Thread(() -> {
            try {
                for (int i = 1; i <= 3; i++) {
                    String feedback = "反馈-" + i;
                    
                    // 与生产者交换数据
                    String received = exchanger.exchange(feedback);
                    log.info("消费者收到产品: {}", received);
                    log.info("消费者发送反馈: {}", feedback);
                    
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        producer.start();
        consumer.start();
        
        producer.join();
        consumer.join();
        
        log.info("Exchanger演示完成\n");
    }

    /**
     * Phaser演示 - 阶段器
     * 用途：更灵活的CyclicBarrier，支持动态调整参与者数量
     */
    private static void demonstratePhaser() throws InterruptedException {
        log.info("--- 5. Phaser演示 ---");
        
        // 创建Phaser，初始参与者为0
        Phaser phaser = new Phaser() {
            @Override
            protected boolean onAdvance(int phase, int registeredParties) {
                log.info("🚀 阶段 {} 完成，参与者数量: {}", phase, registeredParties);
                return phase >= 2; // 完成3个阶段后终止
            }
        };
        
        // 创建工作线程
        Thread[] workers = new Thread[3];
        for (int i = 1; i <= workers.length; i++) {
            final int workerId = i;
            workers[i-1] = new Thread(() -> {
                phaser.register(); // 注册为参与者
                
                try {
                    // 阶段0
                    log.info("工作者 {} 完成阶段0工作", workerId);
                    Thread.sleep(1000);
                    phaser.arriveAndAwaitAdvance(); // 到达并等待其他参与者
                    
                    // 阶段1
                    log.info("工作者 {} 完成阶段1工作", workerId);
                    Thread.sleep(800);
                    phaser.arriveAndAwaitAdvance();
                    
                    // 阶段2
                    log.info("工作者 {} 完成阶段2工作", workerId);
                    Thread.sleep(600);
                    phaser.arriveAndAwaitAdvance();
                    
                    log.info("工作者 {} 全部工作完成", workerId);
                    
                } catch (Exception e) {
                    log.error("工作者 {} 执行出错", workerId, e);
                } finally {
                    phaser.arriveAndDeregister(); // 注销参与者
                }
            });
        }
        
        // 启动所有工作线程
        for (Thread worker : workers) {
            worker.start();
        }
        
        // 等待所有工作线程完成
        for (Thread worker : workers) {
            worker.join();
        }
        
        log.info("Phaser演示完成");
    }
}
