package com.example.multithreading.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 多线程演示控制器
 * 提供Web接口来演示各种多线程概念
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/multithreading")
public class DemoController {

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 获取所有可用的演示
     */
    @GetMapping("/demos")
    public Map<String, Object> getAllDemos() {
        Map<String, Object> demos = new HashMap<>();
        
        demos.put("basic", Map.of(
            "name", "基础线程操作",
            "endpoints", Map.of(
                "thread-creation", "/api/multithreading/basic/thread-creation",
                "thread-states", "/api/multithreading/basic/thread-states"
            )
        ));
        
        demos.put("synchronization", Map.of(
            "name", "同步机制",
            "endpoints", Map.of(
                "synchronized", "/api/multithreading/sync/synchronized",
                "lock", "/api/multithreading/sync/lock"
            )
        ));
        
        demos.put("async", Map.of(
            "name", "异步编程",
            "endpoints", Map.of(
                "completable-future", "/api/multithreading/async/completable-future",
                "parallel-tasks", "/api/multithreading/async/parallel-tasks"
            )
        ));
        
        demos.put("performance", Map.of(
            "name", "性能测试",
            "endpoints", Map.of(
                "benchmark", "/api/multithreading/performance/benchmark"
            )
        ));
        
        return Map.of(
            "message", "多线程编程演示API",
            "demos", demos,
            "documentation", "访问 /swagger-ui.html 查看详细API文档"
        );
    }

    /**
     * 线程创建演示
     */
    @GetMapping("/basic/thread-creation")
    public Map<String, Object> demonstrateThreadCreation() {
        log.info("演示线程创建方式");
        
        Map<String, Object> result = new HashMap<>();
        result.put("timestamp", System.currentTimeMillis());
        result.put("mainThread", Thread.currentThread().getName());
        
        // 方式1：Runnable
        Thread runnableThread = new Thread(() -> {
            log.info("Runnable线程执行: {}", Thread.currentThread().getName());
        });
        runnableThread.start();
        
        // 方式2：CompletableFuture
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            log.info("CompletableFuture线程执行: {}", Thread.currentThread().getName());
            return "异步任务完成";
        });
        
        try {
            runnableThread.join();
            String asyncResult = future.get();
            result.put("asyncResult", asyncResult);
        } catch (Exception e) {
            log.error("线程执行异常", e);
            result.put("error", e.getMessage());
        }
        
        result.put("message", "线程创建演示完成");
        return result;
    }

    /**
     * 线程状态演示
     */
    @GetMapping("/basic/thread-states")
    public Map<String, Object> demonstrateThreadStates() {
        log.info("演示线程状态");
        
        Map<String, Object> result = new HashMap<>();
        Map<String, String> states = new HashMap<>();
        
        // 创建线程但不启动
        Thread thread = new Thread(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        states.put("NEW", thread.getState().toString());
        
        thread.start();
        states.put("RUNNABLE", thread.getState().toString());
        
        try {
            Thread.sleep(100);
            states.put("TIMED_WAITING", thread.getState().toString());
            
            thread.join();
            states.put("TERMINATED", thread.getState().toString());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        result.put("threadStates", states);
        result.put("message", "线程状态演示完成");
        return result;
    }

    /**
     * 异步任务演示
     */
    @GetMapping("/async/completable-future")
    public CompletableFuture<Map<String, Object>> demonstrateCompletableFuture() {
        log.info("演示CompletableFuture异步编程");
        
        return CompletableFuture
            .supplyAsync(() -> {
                log.info("第一步：获取用户数据");
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return "用户数据";
            })
            .thenCompose(userData -> {
                log.info("第二步：处理用户数据 - {}", userData);
                return CompletableFuture.supplyAsync(() -> {
                    try {
                        Thread.sleep(300);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    return userData + " -> 处理完成";
                });
            })
            .thenApply(processedData -> {
                log.info("第三步：格式化结果 - {}", processedData);
                Map<String, Object> result = new HashMap<>();
                result.put("data", processedData);
                result.put("timestamp", System.currentTimeMillis());
                result.put("thread", Thread.currentThread().getName());
                result.put("message", "CompletableFuture演示完成");
                return result;
            })
            .exceptionally(throwable -> {
                log.error("异步任务执行失败", throwable);
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("error", throwable.getMessage());
                errorResult.put("message", "异步任务失败");
                return errorResult;
            });
    }

    /**
     * 并行任务演示
     */
    @GetMapping("/async/parallel-tasks")
    public CompletableFuture<Map<String, Object>> demonstrateParallelTasks() {
        log.info("演示并行任务执行");
        
        // 创建三个并行任务
        CompletableFuture<String> task1 = CompletableFuture.supplyAsync(() -> {
            log.info("任务1开始执行");
            try {
                Thread.sleep(800);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "任务1结果";
        });
        
        CompletableFuture<String> task2 = CompletableFuture.supplyAsync(() -> {
            log.info("任务2开始执行");
            try {
                Thread.sleep(600);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "任务2结果";
        });
        
        CompletableFuture<String> task3 = CompletableFuture.supplyAsync(() -> {
            log.info("任务3开始执行");
            try {
                Thread.sleep(400);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "任务3结果";
        });
        
        // 等待所有任务完成并组合结果
        return CompletableFuture.allOf(task1, task2, task3)
            .thenApply(v -> {
                try {
                    Map<String, Object> result = new HashMap<>();
                    result.put("task1", task1.get());
                    result.put("task2", task2.get());
                    result.put("task3", task3.get());
                    result.put("timestamp", System.currentTimeMillis());
                    result.put("message", "所有并行任务完成");
                    return result;
                } catch (Exception e) {
                    log.error("获取任务结果失败", e);
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("error", e.getMessage());
                    return errorResult;
                }
            });
    }

    /**
     * 性能基准测试
     */
    @GetMapping("/performance/benchmark")
    public Map<String, Object> performanceBenchmark() {
        log.info("执行性能基准测试");
        
        Map<String, Object> result = new HashMap<>();
        
        // 测试不同同步机制的性能
        int iterations = 100_000;
        
        // AtomicInteger测试
        long startTime = System.currentTimeMillis();
        java.util.concurrent.atomic.AtomicInteger atomicCounter = 
            new java.util.concurrent.atomic.AtomicInteger(0);
        
        for (int i = 0; i < iterations; i++) {
            atomicCounter.incrementAndGet();
        }
        long atomicTime = System.currentTimeMillis() - startTime;
        
        // synchronized测试
        startTime = System.currentTimeMillis();
        Counter syncCounter = new Counter();
        for (int i = 0; i < iterations; i++) {
            syncCounter.increment();
        }
        long syncTime = System.currentTimeMillis() - startTime;
        
        result.put("iterations", iterations);
        result.put("atomicTime", atomicTime + "ms");
        result.put("synchronizedTime", syncTime + "ms");
        result.put("speedup", String.format("%.2fx", (double) syncTime / atomicTime));
        result.put("message", "性能基准测试完成");
        
        return result;
    }

    /**
     * 简单的同步计数器
     */
    private static class Counter {
        private int count = 0;
        
        public synchronized void increment() {
            count++;
        }
        
        public synchronized int getCount() {
            return count;
        }
    }
}
