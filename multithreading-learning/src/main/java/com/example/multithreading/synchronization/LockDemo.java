package com.example.multithreading.synchronization;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.locks.*;
import java.util.concurrent.TimeUnit;

/**
 * Lock接口演示
 * 
 * 知识点：
 * 1. ReentrantLock 可重入锁
 * 2. ReadWriteLock 读写锁
 * 3. Condition 条件变量
 * 4. 公平锁 vs 非公平锁
 * 5. 锁的超时机制
 * 6. 可中断锁
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class LockDemo {

    private final ReentrantLock lock = new ReentrantLock();
    private final ReentrantLock fairLock = new ReentrantLock(true); // 公平锁
    private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock();
    private final Lock readLock = readWriteLock.readLock();
    private final Lock writeLock = readWriteLock.writeLock();
    
    private int count = 0;
    private String data = "初始数据";

    public static void main(String[] args) throws InterruptedException {
        LockDemo demo = new LockDemo();
        
        log.info("=== Lock接口演示 ===");
        
        // 1. ReentrantLock基本使用
        demo.demonstrateReentrantLock();
        
        // 2. 锁的超时机制
        demo.demonstrateLockTimeout();
        
        // 3. 可中断锁
        demo.demonstrateInterruptibleLock();
        
        // 4. 公平锁 vs 非公平锁
        demo.demonstrateFairLock();
        
        // 5. 读写锁
        demo.demonstrateReadWriteLock();
        
        // 6. Condition条件变量
        demo.demonstrateCondition();
    }

    /**
     * ReentrantLock基本使用
     */
    private void demonstrateReentrantLock() throws InterruptedException {
        log.info("--- 1. ReentrantLock基本使用 ---");
        
        count = 0;
        
        Thread[] threads = new Thread[5];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    incrementWithLock();
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }
        
        log.info("最终计数: {}", count);
        log.info("ReentrantLock基本使用演示完成\n");
    }

    private void incrementWithLock() {
        lock.lock();
        try {
            count++;
            // 演示可重入性
            reentrantMethod();
        } finally {
            lock.unlock();
        }
    }

    private void reentrantMethod() {
        lock.lock(); // 同一线程可以再次获取锁
        try {
            // 一些操作
        } finally {
            lock.unlock();
        }
    }

    /**
     * 锁的超时机制
     */
    private void demonstrateLockTimeout() throws InterruptedException {
        log.info("--- 2. 锁的超时机制 ---");
        
        Thread longRunningThread = new Thread(() -> {
            lock.lock();
            try {
                log.info("长时间持有锁的线程开始执行");
                Thread.sleep(3000); // 持有锁3秒
                log.info("长时间持有锁的线程执行完成");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                lock.unlock();
            }
        });
        
        Thread timeoutThread = new Thread(() -> {
            try {
                log.info("尝试获取锁，最多等待1秒");
                if (lock.tryLock(1, TimeUnit.SECONDS)) {
                    try {
                        log.info("成功获取锁");
                    } finally {
                        lock.unlock();
                    }
                } else {
                    log.warn("获取锁超时");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("等待锁时被中断");
            }
        });
        
        longRunningThread.start();
        Thread.sleep(100); // 确保第一个线程先获取锁
        timeoutThread.start();
        
        longRunningThread.join();
        timeoutThread.join();
        
        log.info("锁的超时机制演示完成\n");
    }

    /**
     * 可中断锁
     */
    private void demonstrateInterruptibleLock() throws InterruptedException {
        log.info("--- 3. 可中断锁 ---");
        
        Thread blockingThread = new Thread(() -> {
            lock.lock();
            try {
                log.info("阻塞线程获得锁，持有5秒");
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                lock.unlock();
            }
        });
        
        Thread interruptibleThread = new Thread(() -> {
            try {
                log.info("可中断线程尝试获取锁");
                lock.lockInterruptibly(); // 可中断的锁获取
                try {
                    log.info("可中断线程获得锁");
                } finally {
                    lock.unlock();
                }
            } catch (InterruptedException e) {
                log.info("可中断线程在等待锁时被中断");
            }
        });
        
        blockingThread.start();
        Thread.sleep(100);
        interruptibleThread.start();
        
        // 2秒后中断等待锁的线程
        Thread.sleep(2000);
        log.info("中断等待锁的线程");
        interruptibleThread.interrupt();
        
        blockingThread.join();
        interruptibleThread.join();
        
        log.info("可中断锁演示完成\n");
    }

    /**
     * 公平锁 vs 非公平锁
     */
    private void demonstrateFairLock() throws InterruptedException {
        log.info("--- 4. 公平锁 vs 非公平锁 ---");
        
        log.info("非公平锁演示:");
        demonstrateLockFairness(lock, "非公平");
        
        Thread.sleep(1000);
        
        log.info("公平锁演示:");
        demonstrateLockFairness(fairLock, "公平");
        
        log.info("公平锁演示完成\n");
    }

    private void demonstrateLockFairness(ReentrantLock testLock, String lockType) throws InterruptedException {
        Thread[] threads = new Thread[5];
        
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 3; j++) {
                    testLock.lock();
                    try {
                        log.info("{} 锁 - 线程 {} 获得锁，执行第 {} 次", lockType, threadId, j + 1);
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        testLock.unlock();
                    }
                }
            });
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            thread.join();
        }
    }

    /**
     * 读写锁演示
     */
    private void demonstrateReadWriteLock() throws InterruptedException {
        log.info("--- 5. 读写锁演示 ---");
        
        // 创建多个读线程
        Thread[] readers = new Thread[3];
        for (int i = 0; i < readers.length; i++) {
            final int readerId = i;
            readers[i] = new Thread(() -> {
                for (int j = 0; j < 3; j++) {
                    readData(readerId);
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
        }
        
        // 创建写线程
        Thread writer = new Thread(() -> {
            for (int i = 0; i < 3; i++) {
                writeData("新数据-" + i);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        
        // 启动所有线程
        for (Thread reader : readers) {
            reader.start();
        }
        writer.start();
        
        // 等待所有线程完成
        for (Thread reader : readers) {
            reader.join();
        }
        writer.join();
        
        log.info("读写锁演示完成\n");
    }

    private void readData(int readerId) {
        readLock.lock();
        try {
            log.info("读线程 {} 读取数据: {}", readerId, data);
            Thread.sleep(200); // 模拟读取耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            readLock.unlock();
        }
    }

    private void writeData(String newData) {
        writeLock.lock();
        try {
            log.info("写线程更新数据: {} -> {}", data, newData);
            data = newData;
            Thread.sleep(300); // 模拟写入耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * Condition条件变量演示
     */
    private void demonstrateCondition() throws InterruptedException {
        log.info("--- 6. Condition条件变量演示 ---");
        
        ProducerConsumerWithCondition pc = new ProducerConsumerWithCondition();
        
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 5; i++) {
                    pc.produce(i);
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        Thread consumer = new Thread(() -> {
            try {
                for (int i = 1; i <= 5; i++) {
                    pc.consume();
                    Thread.sleep(1500);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        producer.start();
        consumer.start();
        
        producer.join();
        consumer.join();
        
        log.info("Condition条件变量演示完成");
    }

    /**
     * 使用Condition实现的生产者消费者模式
     */
    static class ProducerConsumerWithCondition {
        private final ReentrantLock lock = new ReentrantLock();
        private final Condition notEmpty = lock.newCondition();
        private final Condition notFull = lock.newCondition();
        
        private final int[] buffer = new int[3];
        private int count = 0;
        private int putIndex = 0;
        private int takeIndex = 0;
        
        public void produce(int item) throws InterruptedException {
            lock.lock();
            try {
                while (count == buffer.length) {
                    log.info("缓冲区已满，生产者等待");
                    notFull.await();
                }
                
                buffer[putIndex] = item;
                putIndex = (putIndex + 1) % buffer.length;
                count++;
                
                log.info("生产者生产: {}, 缓冲区大小: {}", item, count);
                notEmpty.signal(); // 通知消费者
                
            } finally {
                lock.unlock();
            }
        }
        
        public int consume() throws InterruptedException {
            lock.lock();
            try {
                while (count == 0) {
                    log.info("缓冲区为空，消费者等待");
                    notEmpty.await();
                }
                
                int item = buffer[takeIndex];
                takeIndex = (takeIndex + 1) % buffer.length;
                count--;
                
                log.info("消费者消费: {}, 缓冲区大小: {}", item, count);
                notFull.signal(); // 通知生产者
                
                return item;
                
            } finally {
                lock.unlock();
            }
        }
    }
}
