package com.example.multithreading.forkjoin;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.Arrays;
import java.util.Random;

/**
 * Fork/Join框架演示
 * 
 * 知识点：
 * 1. Fork/Join框架原理
 * 2. RecursiveTask和RecursiveAction
 * 3. 工作窃取算法
 * 4. 分治算法应用
 * 5. 性能对比
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ForkJoinDemo {

    private static final ForkJoinPool forkJoinPool = new ForkJoinPool();

    public static void main(String[] args) {
        log.info("=== Fork/Join框架演示 ===");
        
        // 1. 数组求和演示
        demonstrateArraySum();
        
        // 2. 数组排序演示
        demonstrateArraySort();
        
        // 3. 斐波那契数列演示
        demonstrateFibonacci();
        
        // 4. 性能对比
        demonstratePerformanceComparison();
        
        // 关闭线程池
        forkJoinPool.shutdown();
    }

    /**
     * 数组求和演示
     */
    private static void demonstrateArraySum() {
        log.info("--- 1. 数组求和演示 ---");
        
        // 创建大数组
        int[] array = new int[10_000_000];
        Random random = new Random();
        for (int i = 0; i < array.length; i++) {
            array[i] = random.nextInt(100);
        }
        
        // 串行求和
        long startTime = System.currentTimeMillis();
        long serialSum = serialSum(array);
        long serialTime = System.currentTimeMillis() - startTime;
        
        // Fork/Join并行求和
        startTime = System.currentTimeMillis();
        SumTask sumTask = new SumTask(array, 0, array.length);
        long parallelSum = forkJoinPool.invoke(sumTask);
        long parallelTime = System.currentTimeMillis() - startTime;
        
        log.info("数组长度: {}", array.length);
        log.info("串行求和结果: {}, 耗时: {} ms", serialSum, serialTime);
        log.info("并行求和结果: {}, 耗时: {} ms", parallelSum, parallelTime);
        log.info("加速比: {:.2f}", (double) serialTime / parallelTime);
        log.info("结果一致性: {}", serialSum == parallelSum ? "✓" : "✗");
        
        log.info("数组求和演示完成\n");
    }

    /**
     * 串行求和
     */
    private static long serialSum(int[] array) {
        long sum = 0;
        for (int value : array) {
            sum += value;
        }
        return sum;
    }

    /**
     * Fork/Join求和任务
     */
    static class SumTask extends RecursiveTask<Long> {
        private static final int THRESHOLD = 10000; // 阈值
        private final int[] array;
        private final int start;
        private final int end;
        
        public SumTask(int[] array, int start, int end) {
            this.array = array;
            this.start = start;
            this.end = end;
        }
        
        @Override
        protected Long compute() {
            int length = end - start;
            
            // 如果任务足够小，直接计算
            if (length <= THRESHOLD) {
                long sum = 0;
                for (int i = start; i < end; i++) {
                    sum += array[i];
                }
                return sum;
            }
            
            // 否则分割任务
            int middle = start + length / 2;
            SumTask leftTask = new SumTask(array, start, middle);
            SumTask rightTask = new SumTask(array, middle, end);
            
            // Fork左任务
            leftTask.fork();
            
            // 计算右任务
            long rightResult = rightTask.compute();
            
            // Join左任务结果
            long leftResult = leftTask.join();
            
            return leftResult + rightResult;
        }
    }

    /**
     * 数组排序演示
     */
    private static void demonstrateArraySort() {
        log.info("--- 2. 数组排序演示 ---");
        
        // 创建随机数组
        int[] array = new int[1_000_000];
        Random random = new Random(42); // 固定种子确保可重复
        for (int i = 0; i < array.length; i++) {
            array[i] = random.nextInt(1000000);
        }
        
        // 复制数组用于对比
        int[] serialArray = array.clone();
        int[] parallelArray = array.clone();
        
        // 串行排序
        long startTime = System.currentTimeMillis();
        Arrays.sort(serialArray);
        long serialTime = System.currentTimeMillis() - startTime;
        
        // Fork/Join并行排序
        startTime = System.currentTimeMillis();
        QuickSortTask sortTask = new QuickSortTask(parallelArray, 0, parallelArray.length - 1);
        forkJoinPool.invoke(sortTask);
        long parallelTime = System.currentTimeMillis() - startTime;
        
        log.info("数组长度: {}", array.length);
        log.info("串行排序耗时: {} ms", serialTime);
        log.info("并行排序耗时: {} ms", parallelTime);
        log.info("加速比: {:.2f}", (double) serialTime / parallelTime);
        log.info("排序结果一致性: {}", Arrays.equals(serialArray, parallelArray) ? "✓" : "✗");
        
        log.info("数组排序演示完成\n");
    }

    /**
     * Fork/Join快速排序任务
     */
    static class QuickSortTask extends RecursiveAction {
        private static final int THRESHOLD = 1000;
        private final int[] array;
        private final int low;
        private final int high;
        
        public QuickSortTask(int[] array, int low, int high) {
            this.array = array;
            this.low = low;
            this.high = high;
        }
        
        @Override
        protected void compute() {
            if (high - low <= THRESHOLD) {
                // 任务足够小，使用串行排序
                Arrays.sort(array, low, high + 1);
                return;
            }
            
            // 分区
            int pivotIndex = partition(array, low, high);
            
            // 创建子任务
            QuickSortTask leftTask = new QuickSortTask(array, low, pivotIndex - 1);
            QuickSortTask rightTask = new QuickSortTask(array, pivotIndex + 1, high);
            
            // 并行执行
            invokeAll(leftTask, rightTask);
        }
        
        private int partition(int[] array, int low, int high) {
            int pivot = array[high];
            int i = low - 1;
            
            for (int j = low; j < high; j++) {
                if (array[j] <= pivot) {
                    i++;
                    swap(array, i, j);
                }
            }
            
            swap(array, i + 1, high);
            return i + 1;
        }
        
        private void swap(int[] array, int i, int j) {
            int temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
    }

    /**
     * 斐波那契数列演示
     */
    private static void demonstrateFibonacci() {
        log.info("--- 3. 斐波那契数列演示 ---");
        
        int n = 40;
        
        // 串行计算
        long startTime = System.currentTimeMillis();
        long serialResult = serialFibonacci(n);
        long serialTime = System.currentTimeMillis() - startTime;
        
        // Fork/Join并行计算
        startTime = System.currentTimeMillis();
        FibonacciTask fibTask = new FibonacciTask(n);
        long parallelResult = forkJoinPool.invoke(fibTask);
        long parallelTime = System.currentTimeMillis() - startTime;
        
        log.info("计算斐波那契数列第 {} 项", n);
        log.info("串行计算结果: {}, 耗时: {} ms", serialResult, serialTime);
        log.info("并行计算结果: {}, 耗时: {} ms", parallelResult, parallelTime);
        log.info("加速比: {:.2f}", (double) serialTime / parallelTime);
        log.info("结果一致性: {}", serialResult == parallelResult ? "✓" : "✗");
        
        log.info("斐波那契数列演示完成\n");
    }

    /**
     * 串行斐波那契计算
     */
    private static long serialFibonacci(int n) {
        if (n <= 1) return n;
        return serialFibonacci(n - 1) + serialFibonacci(n - 2);
    }

    /**
     * Fork/Join斐波那契任务
     */
    static class FibonacciTask extends RecursiveTask<Long> {
        private static final int THRESHOLD = 20;
        private final int n;
        
        public FibonacciTask(int n) {
            this.n = n;
        }
        
        @Override
        protected Long compute() {
            if (n <= THRESHOLD) {
                return serialFibonacci(n);
            }
            
            FibonacciTask f1 = new FibonacciTask(n - 1);
            FibonacciTask f2 = new FibonacciTask(n - 2);
            
            f1.fork();
            long result2 = f2.compute();
            long result1 = f1.join();
            
            return result1 + result2;
        }
    }

    /**
     * 性能对比演示
     */
    private static void demonstratePerformanceComparison() {
        log.info("--- 4. 性能对比演示 ---");
        
        // 测试不同大小的数组
        int[] sizes = {100_000, 1_000_000, 10_000_000};
        
        for (int size : sizes) {
            log.info("测试数组大小: {}", size);
            
            // 创建测试数组
            int[] array = new int[size];
            Random random = new Random();
            for (int i = 0; i < array.length; i++) {
                array[i] = random.nextInt(1000);
            }
            
            // 串行求和
            long startTime = System.currentTimeMillis();
            long serialSum = serialSum(array);
            long serialTime = System.currentTimeMillis() - startTime;
            
            // 并行求和
            startTime = System.currentTimeMillis();
            SumTask sumTask = new SumTask(array, 0, array.length);
            long parallelSum = forkJoinPool.invoke(sumTask);
            long parallelTime = System.currentTimeMillis() - startTime;
            
            double speedup = (double) serialTime / parallelTime;
            
            log.info("  串行耗时: {} ms, 并行耗时: {} ms, 加速比: {:.2f}", 
                serialTime, parallelTime, speedup);
        }
        
        // 测试线程池信息
        log.info("Fork/Join线程池信息:");
        log.info("  并行度: {}", forkJoinPool.getParallelism());
        log.info("  活跃线程数: {}", forkJoinPool.getActiveThreadCount());
        log.info("  运行线程数: {}", forkJoinPool.getRunningThreadCount());
        log.info("  队列任务数: {}", forkJoinPool.getQueuedTaskCount());
        log.info("  窃取任务数: {}", forkJoinPool.getStealCount());
        
        log.info("性能对比演示完成");
    }
}
