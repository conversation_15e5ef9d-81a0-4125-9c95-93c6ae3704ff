package com.example.multithreading.basic;

import lombok.extern.slf4j.Slf4j;

/**
 * 线程状态和生命周期演示
 * 
 * 知识点：
 * 1. 线程的6种状态
 * 2. 状态转换过程
 * 3. 线程生命周期
 * 4. 状态监控
 * 
 * 线程状态：
 * - NEW: 新建状态
 * - RUNNABLE: 可运行状态
 * - BLOCKED: 阻塞状态
 * - WAITING: 等待状态
 * - TIMED_WAITING: 超时等待状态
 * - TERMINATED: 终止状态
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ThreadStateDemo {

    private static final Object lock = new Object();

    public static void main(String[] args) throws InterruptedException {
        log.info("=== 线程状态和生命周期演示 ===");
        
        // 演示所有线程状态
        demonstrateAllThreadStates();
    }

    private static void demonstrateAllThreadStates() throws InterruptedException {
        // 1. NEW 状态
        Thread newThread = new Thread(() -> {
            try {
                // 进入 TIMED_WAITING 状态
                Thread.sleep(2000);
                
                // 尝试获取锁，可能进入 BLOCKED 状态
                synchronized (lock) {
                    log.info("获得锁，执行同步代码块");
                    Thread.sleep(1000);
                }
                
                // 进入 WAITING 状态
                synchronized (lock) {
                    try {
                        lock.wait();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("线程被中断");
            }
        });
        
        log.info("1. NEW 状态 - 线程创建但未启动: {}", newThread.getState());
        
        // 2. RUNNABLE 状态
        newThread.start();
        Thread.sleep(100); // 确保线程开始运行
        log.info("2. RUNNABLE 状态 - 线程正在运行: {}", newThread.getState());
        
        // 3. TIMED_WAITING 状态
        Thread.sleep(500); // 等待线程进入sleep
        log.info("3. TIMED_WAITING 状态 - 线程在sleep: {}", newThread.getState());
        
        // 4. BLOCKED 状态演示
        Thread blockedThread = new Thread(() -> {
            synchronized (lock) {
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        
        // 先启动一个线程占用锁
        blockedThread.start();
        Thread.sleep(100);
        
        // 再启动另一个线程尝试获取锁
        Thread waitingForLockThread = new Thread(() -> {
            synchronized (lock) {
                log.info("终于获得锁了！");
            }
        });
        waitingForLockThread.start();
        Thread.sleep(100);
        
        log.info("4. BLOCKED 状态 - 线程等待获取锁: {}", waitingForLockThread.getState());
        
        // 5. WAITING 状态演示
        Thread waitingThread = new Thread(() -> {
            synchronized (lock) {
                try {
                    log.info("线程进入等待状态");
                    lock.wait(); // 无限期等待
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.info("等待被中断");
                }
            }
        });
        
        waitingThread.start();
        Thread.sleep(100);
        log.info("5. WAITING 状态 - 线程在wait(): {}", waitingThread.getState());
        
        // 等待第一个线程完成
        blockedThread.join();
        
        // 唤醒等待的线程
        synchronized (lock) {
            lock.notifyAll();
        }
        
        // 等待所有线程完成
        waitingForLockThread.join();
        waitingThread.join();
        newThread.join();
        
        // 6. TERMINATED 状态
        log.info("6. TERMINATED 状态 - 线程执行完毕: {}", newThread.getState());
        
        // 详细的状态转换演示
        demonstrateStateTransitions();
    }

    /**
     * 详细的状态转换演示
     */
    private static void demonstrateStateTransitions() throws InterruptedException {
        log.info("\n--- 详细状态转换演示 ---");
        
        StateMonitor monitor = new StateMonitor();
        
        Thread thread = new Thread(() -> {
            try {
                log.info("线程开始执行");
                
                // RUNNABLE -> TIMED_WAITING
                Thread.sleep(1000);
                
                // TIMED_WAITING -> RUNNABLE
                log.info("从睡眠中醒来");
                
                // RUNNABLE -> WAITING
                synchronized (lock) {
                    log.info("进入等待状态");
                    lock.wait();
                }
                
                log.info("被唤醒，继续执行");
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("线程被中断");
            }
        });
        
        // 启动状态监控
        monitor.startMonitoring(thread);
        
        thread.start();
        
        // 让线程运行一段时间
        Thread.sleep(1500);
        
        // 唤醒等待的线程
        synchronized (lock) {
            lock.notify();
        }
        
        thread.join();
        monitor.stopMonitoring();
        
        log.info("状态转换演示完成");
    }

    /**
     * 线程状态监控器
     */
    static class StateMonitor {
        private volatile boolean monitoring = false;
        private Thread monitorThread;
        
        public void startMonitoring(Thread targetThread) {
            monitoring = true;
            monitorThread = new Thread(() -> {
                Thread.State lastState = null;
                while (monitoring) {
                    Thread.State currentState = targetThread.getState();
                    if (lastState != currentState) {
                        log.info("线程状态变化: {} -> {}", 
                            lastState != null ? lastState : "未知", currentState);
                        lastState = currentState;
                    }
                    
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
            monitorThread.start();
        }
        
        public void stopMonitoring() throws InterruptedException {
            monitoring = false;
            if (monitorThread != null) {
                monitorThread.join();
            }
        }
    }
}
