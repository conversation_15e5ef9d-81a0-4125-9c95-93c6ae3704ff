package com.example.multithreading.collections;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.*;

/**
 * 并发集合演示
 * 
 * 知识点：
 * 1. ConcurrentHashMap - 并发哈希表
 * 2. CopyOnWriteArrayList - 写时复制列表
 * 3. ConcurrentLinkedQueue - 并发链表队列
 * 4. BlockingQueue - 阻塞队列
 * 5. ConcurrentSkipListMap - 并发跳表
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ConcurrentCollectionsDemo {

    public static void main(String[] args) throws InterruptedException {
        log.info("=== 并发集合演示 ===");
        
        // 1. ConcurrentHashMap演示
        demonstrateConcurrentHashMap();
        
        // 2. CopyOnWriteArrayList演示
        demonstrateCopyOnWriteArrayList();
        
        // 3. ConcurrentLinkedQueue演示
        demonstrateConcurrentLinkedQueue();
        
        // 4. BlockingQueue演示
        demonstrateBlockingQueue();
        
        // 5. ConcurrentSkipListMap演示
        demonstrateConcurrentSkipListMap();
        
        // 6. 性能对比
        demonstratePerformanceComparison();
    }

    /**
     * ConcurrentHashMap演示
     */
    private static void demonstrateConcurrentHashMap() throws InterruptedException {
        log.info("--- 1. ConcurrentHashMap演示 ---");
        
        ConcurrentHashMap<String, Integer> concurrentMap = new ConcurrentHashMap<>();
        
        // 基本操作
        concurrentMap.put("key1", 1);
        concurrentMap.put("key2", 2);
        log.info("初始map: {}", concurrentMap);
        
        // 原子操作
        Integer oldValue = concurrentMap.putIfAbsent("key3", 3);
        log.info("putIfAbsent结果: {}, 当前map: {}", oldValue, concurrentMap);
        
        // 替换操作
        boolean replaced = concurrentMap.replace("key1", 1, 10);
        log.info("replace操作: {}, 当前map: {}", replaced, concurrentMap);
        
        // 计算操作
        concurrentMap.compute("key4", (k, v) -> v == null ? 1 : v + 1);
        concurrentMap.compute("key4", (k, v) -> v == null ? 1 : v + 1);
        log.info("compute操作后: {}", concurrentMap);
        
        // 多线程并发操作
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < 100; j++) {
                    String key = "thread-" + threadId;
                    concurrentMap.compute(key, (k, v) -> v == null ? 1 : v + 1);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        log.info("多线程操作后map大小: {}", concurrentMap.size());
        concurrentMap.forEach((k, v) -> log.info("  {}: {}", k, v));
        
        log.info("ConcurrentHashMap演示完成\n");
    }

    /**
     * CopyOnWriteArrayList演示
     */
    private static void demonstrateCopyOnWriteArrayList() throws InterruptedException {
        log.info("--- 2. CopyOnWriteArrayList演示 ---");
        
        CopyOnWriteArrayList<String> cowList = new CopyOnWriteArrayList<>();
        
        // 添加初始数据
        cowList.add("元素1");
        cowList.add("元素2");
        cowList.add("元素3");
        log.info("初始列表: {}", cowList);
        
        // 演示读多写少的场景
        CountDownLatch latch = new CountDownLatch(11);
        
        // 10个读线程
        for (int i = 0; i < 10; i++) {
            final int readerId = i;
            new Thread(() -> {
                try {
                    for (int j = 0; j < 5; j++) {
                        // 遍历列表（读操作）
                        for (String item : cowList) {
                            // 模拟读取操作
                        }
                        log.info("读线程 {} 完成第 {} 次读取，列表大小: {}", 
                            readerId, j + 1, cowList.size());
                        Thread.sleep(100);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        // 1个写线程
        new Thread(() -> {
            try {
                for (int i = 0; i < 3; i++) {
                    cowList.add("新元素-" + i);
                    log.info("写线程添加: 新元素-{}, 当前列表大小: {}", i, cowList.size());
                    Thread.sleep(200);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        }).start();
        
        latch.await();
        log.info("最终列表: {}", cowList);
        
        log.info("CopyOnWriteArrayList演示完成\n");
    }

    /**
     * ConcurrentLinkedQueue演示
     */
    private static void demonstrateConcurrentLinkedQueue() throws InterruptedException {
        log.info("--- 3. ConcurrentLinkedQueue演示 ---");
        
        ConcurrentLinkedQueue<String> queue = new ConcurrentLinkedQueue<>();
        
        // 生产者-消费者模式
        CountDownLatch latch = new CountDownLatch(3);
        
        // 生产者线程
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 10; i++) {
                    String item = "Item-" + i;
                    queue.offer(item);
                    log.info("生产者添加: {}, 队列大小: {}", item, queue.size());
                    Thread.sleep(100);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        // 消费者线程1
        Thread consumer1 = new Thread(() -> {
            try {
                while (true) {
                    String item = queue.poll();
                    if (item != null) {
                        log.info("消费者1获取: {}, 剩余: {}", item, queue.size());
                    } else {
                        Thread.sleep(50);
                    }
                    
                    // 简单的退出条件
                    if (queue.isEmpty() && !producer.isAlive()) {
                        break;
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        // 消费者线程2
        Thread consumer2 = new Thread(() -> {
            try {
                while (true) {
                    String item = queue.poll();
                    if (item != null) {
                        log.info("消费者2获取: {}, 剩余: {}", item, queue.size());
                    } else {
                        Thread.sleep(50);
                    }
                    
                    if (queue.isEmpty() && !producer.isAlive()) {
                        break;
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        producer.start();
        consumer1.start();
        consumer2.start();
        
        latch.await();
        log.info("最终队列大小: {}", queue.size());
        
        log.info("ConcurrentLinkedQueue演示完成\n");
    }

    /**
     * BlockingQueue演示
     */
    private static void demonstrateBlockingQueue() throws InterruptedException {
        log.info("--- 4. BlockingQueue演示 ---");
        
        // ArrayBlockingQueue - 有界阻塞队列
        BlockingQueue<String> arrayQueue = new ArrayBlockingQueue<>(3);
        
        log.info("4.1 ArrayBlockingQueue演示:");
        demonstrateBlockingQueueOperations(arrayQueue, "ArrayBlockingQueue");
        
        // LinkedBlockingQueue - 可选有界阻塞队列
        BlockingQueue<String> linkedQueue = new LinkedBlockingQueue<>(3);
        
        log.info("4.2 LinkedBlockingQueue演示:");
        demonstrateBlockingQueueOperations(linkedQueue, "LinkedBlockingQueue");
        
        // PriorityBlockingQueue - 优先级阻塞队列
        log.info("4.3 PriorityBlockingQueue演示:");
        PriorityBlockingQueue<Task> priorityQueue = new PriorityBlockingQueue<>();
        
        // 添加不同优先级的任务
        priorityQueue.put(new Task("低优先级任务", 3));
        priorityQueue.put(new Task("高优先级任务", 1));
        priorityQueue.put(new Task("中优先级任务", 2));
        
        // 按优先级取出任务
        while (!priorityQueue.isEmpty()) {
            Task task = priorityQueue.take();
            log.info("取出任务: {}", task);
        }
        
        log.info("BlockingQueue演示完成\n");
    }

    private static void demonstrateBlockingQueueOperations(BlockingQueue<String> queue, String queueType) 
            throws InterruptedException {
        
        CountDownLatch latch = new CountDownLatch(2);
        
        // 生产者
        Thread producer = new Thread(() -> {
            try {
                for (int i = 1; i <= 5; i++) {
                    String item = "Item-" + i;
                    queue.put(item); // 阻塞式添加
                    log.info("{} 生产者添加: {}, 队列大小: {}", queueType, item, queue.size());
                    Thread.sleep(200);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        // 消费者
        Thread consumer = new Thread(() -> {
            try {
                for (int i = 1; i <= 5; i++) {
                    String item = queue.take(); // 阻塞式获取
                    log.info("{} 消费者获取: {}, 队列大小: {}", queueType, item, queue.size());
                    Thread.sleep(300);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                latch.countDown();
            }
        });
        
        producer.start();
        Thread.sleep(100); // 让生产者先运行
        consumer.start();
        
        latch.await();
    }

    /**
     * 任务类，用于优先级队列演示
     */
    static class Task implements Comparable<Task> {
        private final String name;
        private final int priority;
        
        public Task(String name, int priority) {
            this.name = name;
            this.priority = priority;
        }
        
        @Override
        public int compareTo(Task other) {
            return Integer.compare(this.priority, other.priority);
        }
        
        @Override
        public String toString() {
            return String.format("%s (优先级: %d)", name, priority);
        }
    }

    /**
     * ConcurrentSkipListMap演示
     */
    private static void demonstrateConcurrentSkipListMap() throws InterruptedException {
        log.info("--- 5. ConcurrentSkipListMap演示 ---");
        
        ConcurrentSkipListMap<Integer, String> skipListMap = new ConcurrentSkipListMap<>();
        
        // 添加数据
        skipListMap.put(3, "三");
        skipListMap.put(1, "一");
        skipListMap.put(4, "四");
        skipListMap.put(2, "二");
        skipListMap.put(5, "五");
        
        log.info("有序map: {}", skipListMap);
        
        // 范围查询
        log.info("子map [2, 4]: {}", skipListMap.subMap(2, true, 4, true));
        log.info("头部map (<= 3): {}", skipListMap.headMap(3, true));
        log.info("尾部map (>= 3): {}", skipListMap.tailMap(3, true));
        
        // 并发操作
        CountDownLatch latch = new CountDownLatch(5);
        for (int i = 0; i < 5; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < 10; j++) {
                    int key = threadId * 10 + j;
                    skipListMap.put(key, "值-" + key);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        log.info("并发操作后map大小: {}", skipListMap.size());
        log.info("前10个元素: {}", skipListMap.headMap(10, false));
        
        log.info("ConcurrentSkipListMap演示完成\n");
    }

    /**
     * 性能对比演示
     */
    private static void demonstratePerformanceComparison() throws InterruptedException {
        log.info("--- 6. 性能对比演示 ---");
        
        int threadCount = 10;
        int operationsPerThread = 10000;
        
        // HashMap vs ConcurrentHashMap
        log.info("Map性能对比 ({} 线程, 每线程 {} 次操作):", threadCount, operationsPerThread);
        
        // 测试ConcurrentHashMap
        long startTime = System.currentTimeMillis();
        testConcurrentHashMap(threadCount, operationsPerThread);
        long concurrentMapTime = System.currentTimeMillis() - startTime;
        
        // 测试Collections.synchronizedMap
        startTime = System.currentTimeMillis();
        testSynchronizedMap(threadCount, operationsPerThread);
        long synchronizedMapTime = System.currentTimeMillis() - startTime;
        
        log.info("  ConcurrentHashMap: {} ms", concurrentMapTime);
        log.info("  SynchronizedMap: {} ms", synchronizedMapTime);
        
        // List性能对比
        log.info("List性能对比:");
        
        // 测试CopyOnWriteArrayList (读多写少场景)
        startTime = System.currentTimeMillis();
        testCopyOnWriteArrayList();
        long cowListTime = System.currentTimeMillis() - startTime;
        
        // 测试Collections.synchronizedList
        startTime = System.currentTimeMillis();
        testSynchronizedList();
        long synchronizedListTime = System.currentTimeMillis() - startTime;
        
        log.info("  CopyOnWriteArrayList: {} ms", cowListTime);
        log.info("  SynchronizedList: {} ms", synchronizedListTime);
        
        log.info("性能对比演示完成");
    }

    private static void testConcurrentHashMap(int threadCount, int operationsPerThread) throws InterruptedException {
        ConcurrentHashMap<Integer, Integer> map = new ConcurrentHashMap<>();
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    map.put(threadId * operationsPerThread + j, j);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
    }

    private static void testSynchronizedMap(int threadCount, int operationsPerThread) throws InterruptedException {
        Map<Integer, Integer> map = Collections.synchronizedMap(new HashMap<>());
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    map.put(threadId * operationsPerThread + j, j);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
    }

    private static void testCopyOnWriteArrayList() throws InterruptedException {
        CopyOnWriteArrayList<Integer> list = new CopyOnWriteArrayList<>();
        CountDownLatch latch = new CountDownLatch(11);
        
        // 10个读线程
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    for (Integer item : list) {
                        // 读操作
                    }
                }
                latch.countDown();
            }).start();
        }
        
        // 1个写线程
        new Thread(() -> {
            for (int i = 0; i < 100; i++) {
                list.add(i);
            }
            latch.countDown();
        }).start();
        
        latch.await();
    }

    private static void testSynchronizedList() throws InterruptedException {
        List<Integer> list = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(11);
        
        // 10个读线程
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    synchronized (list) {
                        for (Integer item : list) {
                            // 读操作
                        }
                    }
                }
                latch.countDown();
            }).start();
        }
        
        // 1个写线程
        new Thread(() -> {
            for (int i = 0; i < 100; i++) {
                list.add(i);
            }
            latch.countDown();
        }).start();
        
        latch.await();
    }
}
