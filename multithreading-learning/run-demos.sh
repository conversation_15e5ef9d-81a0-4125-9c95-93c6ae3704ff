#!/bin/bash

# Java多线程编程学习项目演示脚本

echo ""
echo "🧵 Java多线程编程学习项目演示脚本"
echo "====================================="
echo ""

# 设置类路径
CLASSPATH="target/classes:$HOME/.m2/repository/ch/qos/logback/logback-classic/1.4.7/logback-classic-1.4.7.jar:$HOME/.m2/repository/ch/qos/logback/logback-core/1.4.7/logback-core-1.4.7.jar:$HOME/.m2/repository/org/slf4j/slf4j-api/2.0.7/slf4j-api-2.0.7.jar"

show_menu() {
    echo "请选择要运行的演示："
    echo ""
    echo "1. 基础线程操作演示"
    echo "2. 同步机制演示"
    echo "3. 线程池演示"
    echo "4. 并发工具类演示"
    echo "5. 原子操作演示"
    echo "6. 并发集合演示"
    echo "7. CompletableFuture异步编程演示"
    echo "8. Fork/Join框架演示"
    echo "9. 生产者消费者模式演示"
    echo "10. 性能基准测试"
    echo "11. 启动Web应用"
    echo "12. 运行所有演示"
    echo "0. 退出"
    echo ""
}

run_demo() {
    local demo_class=$1
    local demo_name=$2
    
    echo ""
    echo "🔄 运行 $demo_name..."
    java -cp "$CLASSPATH" "$demo_class"
    echo ""
    read -p "按回车键继续..."
}

run_all_demos() {
    echo ""
    echo "🎯 运行所有演示（按顺序执行）..."
    echo ""
    
    java -cp "$CLASSPATH" com.example.multithreading.basic.ThreadCreationDemo
    echo "✅ 基础线程操作演示完成"
    echo ""
    
    java -cp "$CLASSPATH" com.example.multithreading.synchronization.SynchronizedDemo
    echo "✅ 同步机制演示完成"
    echo ""
    
    java -cp "$CLASSPATH" com.example.multithreading.async.CompletableFutureDemo
    echo "✅ 异步编程演示完成"
    echo ""
    
    java -cp "$CLASSPATH" com.example.multithreading.patterns.ProducerConsumerDemo
    echo "✅ 生产者消费者模式演示完成"
    echo ""
    
    java -cp "$CLASSPATH" com.example.multithreading.benchmark.SimpleBenchmark
    echo "✅ 性能基准测试完成"
    echo ""
    
    echo "🎉 所有演示运行完成！"
    read -p "按回车键继续..."
}

start_webapp() {
    echo ""
    echo "🌐 启动Web应用..."
    echo "应用将在 http://localhost:8090 启动"
    echo "可以访问以下API接口："
    echo "  - http://localhost:8090/api/multithreading/demos"
    echo "  - http://localhost:8090/api/multithreading/async/completable-future"
    echo "  - http://localhost:8090/api/multithreading/performance/benchmark"
    echo ""
    echo "按 Ctrl+C 停止应用"
    cd ..
    mvn spring-boot:run -pl multithreading-learning
}

while true; do
    show_menu
    read -p "请输入选择 (0-12): " choice
    
    case $choice in
        1)
            run_demo "com.example.multithreading.basic.ThreadCreationDemo" "基础线程操作演示"
            ;;
        2)
            run_demo "com.example.multithreading.synchronization.SynchronizedDemo" "同步机制演示"
            ;;
        3)
            run_demo "com.example.multithreading.threadpool.ThreadPoolDemo" "线程池演示"
            ;;
        4)
            run_demo "com.example.multithreading.concurrent.ConcurrentUtilsDemo" "并发工具类演示"
            ;;
        5)
            run_demo "com.example.multithreading.atomic.AtomicDemo" "原子操作演示"
            ;;
        6)
            run_demo "com.example.multithreading.collections.ConcurrentCollectionsDemo" "并发集合演示"
            ;;
        7)
            run_demo "com.example.multithreading.async.CompletableFutureDemo" "CompletableFuture异步编程演示"
            ;;
        8)
            run_demo "com.example.multithreading.forkjoin.ForkJoinDemo" "Fork/Join框架演示"
            ;;
        9)
            run_demo "com.example.multithreading.patterns.ProducerConsumerDemo" "生产者消费者模式演示"
            ;;
        10)
            run_demo "com.example.multithreading.benchmark.SimpleBenchmark" "性能基准测试"
            ;;
        11)
            start_webapp
            ;;
        12)
            run_all_demos
            ;;
        0)
            echo ""
            echo "👋 感谢使用Java多线程编程学习项目！"
            echo ""
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
done
