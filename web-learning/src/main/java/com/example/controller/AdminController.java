package com.example.controller;

import com.example.client.UserClient;
import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
@RequiredArgsConstructor
public class AdminController {
    private final UserClient userClient;
    
    @GetMapping("/users")
    public String userList(@RequestParam(defaultValue = "1") int page,
                         @RequestParam(defaultValue = "10") int size,
                         @RequestParam(required = false) String keyword,
                         Model model) {
        // TODO: 实现用户列表查询
        // 暂时使用空列表，等待 UserClient 接口完善
        model.addAttribute("users", List.of());
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", 0);
        model.addAttribute("keyword", keyword);
        return "admin/users";
    }
    
    @GetMapping("/roles")
    public String roleList(Model model) {
        // TODO: 实现角色列表查询
        // 暂时使用空列表，等待 UserClient 接口完善
        model.addAttribute("roles", List.of());
        return "admin/roles";
    }
    
    @GetMapping("/settings")
    public String settings(Model model) {
        // 系统设置页面
        return "admin/settings";
    }
} 