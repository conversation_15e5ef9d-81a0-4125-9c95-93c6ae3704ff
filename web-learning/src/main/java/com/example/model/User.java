package com.example.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 简化的用户模型
 * 用于 web-learning 模块的本地用户管理
 * 注意：这是一个简化版本，生产环境建议使用 user-center 的 UserDTO
 */
@Data
public class User {
    private Long id;
    private String username;
    private String password;
    private String nickname;
    private String email;
    private String avatar;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
