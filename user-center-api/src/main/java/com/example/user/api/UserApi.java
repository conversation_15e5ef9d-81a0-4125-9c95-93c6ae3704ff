package com.example.user.api;

import com.example.common.model.Result;
import com.example.user.dto.UserDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户管理", description = "用户相关操作接口，包括注册、登录、认证、用户信息管理等功能")
@Validated
public interface UserApi {

    @Operation(
        summary = "用户注册",
        description = "创建新用户账户。用户名必须唯一，密码会自动加密存储。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "注册成功",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Result.class),
                examples = @ExampleObject(
                    name = "成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "测试用户",
                        "email": "<EMAIL>",
                        "avatar": null,
                        "createdAt": "2023-12-11T10:30:00",
                        "updatedAt": "2023-12-11T10:30:00"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "参数错误",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "参数错误示例",
                    value = """
                    {
                      "success": false,
                      "code": "400",
                      "message": "用户名已存在",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @PostMapping("/api/users/register")
    Result<UserDTO> register(
        @Parameter(
            description = "用户注册信息",
            required = true,
            schema = @Schema(implementation = UserDTO.class)
        )
        @Validated @RequestBody UserDTO userDTO
    );

    @Operation(
        summary = "用户登录",
        description = "用户登录验证，成功后返回 JWT Token。Token 有效期为 24 小时。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "登录成功",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "登录成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYzOTIwMDAwMCwiZXhwIjoxNjM5Mjg2NDAwfQ.signature"
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "认证失败",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "认证失败示例",
                    value = """
                    {
                      "success": false,
                      "code": "401",
                      "message": "用户名或密码错误",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @PostMapping("/api/users/login")
    Result<String> login(
        @Parameter(description = "用户名", required = true, example = "testuser")
        @RequestParam("username") String username,

        @Parameter(description = "密码", required = true, example = "123456")
        @RequestParam("password") String password
    );

    @Operation(
        summary = "Token 验证",
        description = "验证 JWT Token 的有效性，并返回当前用户信息。需要在请求头中携带 Authorization。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Token 有效",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "验证成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "测试用户",
                        "email": "<EMAIL>",
                        "avatar": null,
                        "createdAt": "2023-12-11T10:30:00",
                        "updatedAt": "2023-12-11T10:30:00"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Token 无效或已过期",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "Token 无效示例",
                    value = """
                    {
                      "success": false,
                      "code": "401",
                      "message": "Token 无效或已过期",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @GetMapping("/api/users/validate")
    Result<UserDTO> validateToken(
        @Parameter(
            description = "JWT Token，格式：Bearer {token}",
            required = true,
            example = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImlhdCI6MTYzOTIwMDAwMCwiZXhwIjoxNjM5Mjg2NDAwfQ.signature"
        )
        @RequestHeader("Authorization") String token
    );

    @Operation(
        summary = "更新用户信息",
        description = "更新指定用户的信息。需要管理员权限或用户本人权限。密码字段如果为空则不更新。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "更新成功",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "更新成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "新昵称",
                        "email": "<EMAIL>",
                        "avatar": null,
                        "createdAt": "2023-12-11T10:30:00",
                        "updatedAt": "2023-12-11T15:45:00"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "用户不存在",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "用户不存在示例",
                    value = """
                    {
                      "success": false,
                      "code": "404",
                      "message": "用户不存在",
                      "data": null
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "权限不足",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "权限不足示例",
                    value = """
                    {
                      "success": false,
                      "code": "403",
                      "message": "权限不足",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @PutMapping("/api/users/{id}")
    Result<UserDTO> update(
        @Parameter(description = "用户ID", required = true, example = "1")
        @PathVariable("id") Long id,

        @Parameter(
            description = "更新的用户信息",
            required = true,
            schema = @Schema(implementation = UserDTO.class)
        )
        @Validated @RequestBody UserDTO userDTO
    );

    @Operation(
        summary = "删除用户",
        description = "删除指定的用户账户。此操作不可逆，需要管理员权限。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "删除成功",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "删除成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": null
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "用户不存在",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "用户不存在示例",
                    value = """
                    {
                      "success": false,
                      "code": "404",
                      "message": "用户不存在",
                      "data": null
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "权限不足",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "权限不足示例",
                    value = """
                    {
                      "success": false,
                      "code": "403",
                      "message": "权限不足",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @DeleteMapping("/api/users/{id}")
    Result<Void> delete(
        @Parameter(description = "要删除的用户ID", required = true, example = "1")
        @PathVariable("id") Long id
    );

    @Operation(
        summary = "获取用户信息",
        description = "根据用户ID获取用户详细信息。需要认证，用户只能查看自己的信息，管理员可以查看所有用户信息。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "查询成功",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "查询成功示例",
                    value = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "操作成功",
                      "data": {
                        "id": 1,
                        "username": "testuser",
                        "nickname": "测试用户",
                        "email": "<EMAIL>",
                        "avatar": "https://example.com/avatar.jpg",
                        "createdAt": "2023-12-11T10:30:00",
                        "updatedAt": "2023-12-11T10:30:00"
                      }
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "用户不存在",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "用户不存在示例",
                    value = """
                    {
                      "success": false,
                      "code": "404",
                      "message": "用户不存在",
                      "data": null
                    }
                    """
                )
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "权限不足",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "权限不足示例",
                    value = """
                    {
                      "success": false,
                      "code": "403",
                      "message": "权限不足",
                      "data": null
                    }
                    """
                )
            )
        )
    })
    @GetMapping("/api/users/{id}")
    Result<UserDTO> findById(
        @Parameter(description = "用户ID", required = true, example = "1")
        @PathVariable("id") Long id
    );
}