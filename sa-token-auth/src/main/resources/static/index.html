<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sa-Token 权限控制系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #ff6b6b;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 5px;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .user-info {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        
        .user-info h3 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        
        .user-info p {
            margin-bottom: 0.25rem;
            color: #666;
        }
        
        .default-accounts {
            margin-top: 1rem;
            padding: 1rem;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .default-accounts h4 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        
        .default-accounts p {
            margin-bottom: 0.25rem;
            color: #666;
        }
        
        .actions {
            margin-top: 1rem;
            display: none;
        }
        
        .actions button {
            width: 100%;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .actions button:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sa-Token</h1>
            <p>权限控制系统 - 端口 8082</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">登录</button>
        </form>
        
        <div id="result" class="result"></div>
        <div id="userInfo" class="user-info"></div>
        
        <div id="actions" class="actions">
            <button onclick="getUserInfo()">获取用户信息</button>
            <button onclick="checkLogin()">检查登录状态</button>
            <button onclick="getTokenInfo()">获取Token信息</button>
            <button onclick="logout()">登出</button>
        </div>
        
        <div class="default-accounts">
            <h4>默认测试账号</h4>
            <p><strong>管理员：</strong>admin / admin123</p>
            <p><strong>普通用户：</strong>testuser / test123</p>
        </div>
    </div>

    <script>
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const result = document.getElementById('result');
        const userInfo = document.getElementById('userInfo');
        const actions = document.getElementById('actions');
        
        let currentToken = null;
        
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch('/sa-token-auth/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    currentToken = data.data.token;
                    showResult('登录成功！', 'success');
                    showUserInfo(data.data.userInfo);
                    actions.style.display = 'block';
                    loginBtn.textContent = '重新登录';
                } else {
                    showResult('登录失败：' + data.message, 'error');
                }
            } catch (error) {
                showResult('网络错误：' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                if (loginBtn.textContent === '登录中...') {
                    loginBtn.textContent = '登录';
                }
            }
        });
        
        function showResult(message, type) {
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
        
        function showUserInfo(info) {
            userInfo.innerHTML = `
                <h3>用户信息</h3>
                <p><strong>ID:</strong> ${info.id}</p>
                <p><strong>用户名:</strong> ${info.username}</p>
                <p><strong>邮箱:</strong> ${info.email || '未设置'}</p>
                <p><strong>真实姓名:</strong> ${info.realName || '未设置'}</p>
                <p><strong>角色:</strong> ${info.roles.join(', ')}</p>
                <p><strong>权限:</strong> ${info.permissions.join(', ')}</p>
            `;
            userInfo.style.display = 'block';
        }
        
        async function getUserInfo() {
            if (!currentToken) return;

            try {
                const response = await fetch('/sa-token-auth/auth/me', {
                    headers: {
                        'satoken': currentToken
                    }
                });

                const data = await response.json();

                if (data.code === 200) {
                    showResult('获取用户信息成功！', 'success');
                    showUserInfo(data.data);
                } else {
                    showResult('获取用户信息失败：' + data.message, 'error');
                }
            } catch (error) {
                showResult('网络错误：' + error.message, 'error');
            }
        }
        
        async function checkLogin() {
            if (!currentToken) return;

            try {
                const response = await fetch('/sa-token-auth/auth/check', {
                    headers: {
                        'satoken': currentToken
                    }
                });

                const data = await response.json();
                showResult('登录状态：' + data.message, data.code === 200 ? 'success' : 'error');
            } catch (error) {
                showResult('网络错误：' + error.message, 'error');
            }
        }
        
        async function getTokenInfo() {
            if (!currentToken) return;

            try {
                const response = await fetch('/sa-token-auth/auth/token-info', {
                    headers: {
                        'satoken': currentToken
                    }
                });

                const data = await response.json();

                if (data.code === 200) {
                    showResult('Token信息获取成功！查看控制台', 'success');
                    console.log('Token信息：', data.data);
                } else {
                    showResult('获取Token信息失败：' + data.message, 'error');
                }
            } catch (error) {
                showResult('网络错误：' + error.message, 'error');
            }
        }
        
        async function logout() {
            if (!currentToken) return;

            try {
                const response = await fetch('/sa-token-auth/auth/logout', {
                    method: 'POST',
                    headers: {
                        'satoken': currentToken
                    }
                });

                const data = await response.json();

                if (data.code === 200) {
                    showResult('登出成功！', 'success');
                    currentToken = null;
                    actions.style.display = 'none';
                    userInfo.style.display = 'none';
                    loginBtn.textContent = '登录';
                } else {
                    showResult('登出失败：' + data.message, 'error');
                }
            } catch (error) {
                showResult('网络错误：' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
