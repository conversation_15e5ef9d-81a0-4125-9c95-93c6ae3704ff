package com.example.satoken.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.example.satoken.dto.LoginRequest;
import com.example.satoken.dto.LoginResponse;
import com.example.satoken.dto.RegisterRequest;
import com.example.satoken.entity.SysPermission;
import com.example.satoken.entity.SysRole;
import com.example.satoken.entity.SysUser;
import com.example.satoken.repository.SysPermissionRepository;
import com.example.satoken.repository.SysRoleRepository;
import com.example.satoken.repository.SysUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 认证服务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final SysUserRepository userRepository;
    private final SysRoleRepository roleRepository;
    private final SysPermissionRepository permissionRepository;
    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @Transactional(readOnly = true)
    public LoginResponse login(LoginRequest loginRequest) {
        log.info("User login attempt: {}", loginRequest.getUsername());
        
        // 查找用户
        SysUser user = userRepository.findByUsernameWithRolesAndPermissions(loginRequest.getUsername())
                .orElseThrow(() -> new RuntimeException("用户名或密码错误"));

        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new RuntimeException("用户已被禁用");
        }

        // 验证密码
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // Sa-Token 登录
        StpUtil.login(user.getId());
        
        // 获取Token信息
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        
        // 构建用户信息
        LoginResponse.UserInfo userInfo = buildUserInfo(user);

        log.info("User login successful: {}", loginRequest.getUsername());
        return new LoginResponse(tokenInfo.getTokenValue(), tokenInfo.getTokenTimeout(), userInfo);
    }

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 用户信息
     */
    @Transactional
    public SysUser register(RegisterRequest registerRequest) {
        log.info("User registration attempt: {}", registerRequest.getUsername());
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registerRequest.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (registerRequest.getEmail() != null && userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        // 检查手机号是否已存在
        if (registerRequest.getPhone() != null && userRepository.existsByPhone(registerRequest.getPhone())) {
            throw new RuntimeException("手机号已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());
        user.setPhone(registerRequest.getPhone());
        user.setRealName(registerRequest.getRealName());
        user.setStatus(1);

        // 分配默认角色（USER）
        SysRole userRole = roleRepository.findByRoleCode("USER")
                .orElseThrow(() -> new RuntimeException("默认角色不存在"));
        user.setRoles(Set.of(userRole));

        SysUser savedUser = userRepository.save(user);
        log.info("User registration successful: {}", registerRequest.getUsername());
        
        return savedUser;
    }

    /**
     * 用户登出
     */
    public void logout() {
        try {
            Object loginId = StpUtil.getLoginIdDefaultNull();
            if (loginId != null) {
                log.info("User logout: {}", loginId);
                StpUtil.logout();
            }
        } catch (Exception e) {
            log.error("Logout error", e);
        }
    }

    /**
     * 获取当前登录用户信息
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public LoginResponse.UserInfo getCurrentUserInfo() {
        Object loginId = StpUtil.getLoginIdDefaultNull();
        if (loginId == null) {
            throw new RuntimeException("用户未登录");
        }

        Long userId = Long.valueOf(loginId.toString());
        SysUser user = userRepository.findByUsernameWithRolesAndPermissions(
                userRepository.findById(userId)
                        .orElseThrow(() -> new RuntimeException("用户不存在"))
                        .getUsername()
        ).orElseThrow(() -> new RuntimeException("用户不存在"));

        return buildUserInfo(user);
    }

    /**
     * 构建用户信息
     * @param user 用户实体
     * @return 用户信息DTO
     */
    private LoginResponse.UserInfo buildUserInfo(SysUser user) {
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setEmail(user.getEmail());
        userInfo.setPhone(user.getPhone());
        userInfo.setRealName(user.getRealName());
        userInfo.setAvatar(user.getAvatar());

        // 获取角色列表
        List<String> roles = user.getRoles().stream()
                .filter(role -> role.getStatus() == 1)
                .map(SysRole::getRoleCode)
                .collect(Collectors.toList());
        userInfo.setRoles(roles);

        // 获取权限列表
        List<String> permissions = user.getRoles().stream()
                .filter(role -> role.getStatus() == 1)
                .flatMap(role -> role.getPermissions().stream())
                .filter(permission -> permission.getStatus() == 1)
                .map(SysPermission::getPermissionCode)
                .distinct()
                .collect(Collectors.toList());
        userInfo.setPermissions(permissions);

        return userInfo;
    }
}
