package com.example.satoken.repository;

import com.example.satoken.entity.SysPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 权限数据访问层
 * 
 * <AUTHOR> Team
 */
@Repository
public interface SysPermissionRepository extends JpaRepository<SysPermission, Long> {

    /**
     * 根据权限编码查找权限
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    Optional<SysPermission> findByPermissionCode(String permissionCode);

    /**
     * 查找所有启用的权限
     * @return 权限列表
     */
    List<SysPermission> findByStatus(Integer status);

    /**
     * 根据资源类型查找权限
     * @param resourceType 资源类型
     * @return 权限列表
     */
    List<SysPermission> findByResourceType(String resourceType);

    /**
     * 根据父权限ID查找子权限
     * @param parentId 父权限ID
     * @return 权限列表
     */
    List<SysPermission> findByParentIdOrderBySortOrder(Long parentId);

    /**
     * 检查权限编码是否存在
     * @param permissionCode 权限编码
     * @return 是否存在
     */
    boolean existsByPermissionCode(String permissionCode);

    /**
     * 根据用户ID查找权限列表
     * @param userId 用户ID
     * @return 权限列表
     */
    @Query("SELECT DISTINCT p FROM SysPermission p " +
           "JOIN p.roles r " +
           "JOIN r.users u " +
           "WHERE u.id = :userId AND p.status = 1 AND r.status = 1")
    List<SysPermission> findByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查找权限列表
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Query("SELECT p FROM SysPermission p " +
           "JOIN p.roles r " +
           "WHERE r.id = :roleId AND p.status = 1")
    List<SysPermission> findByRoleId(@Param("roleId") Long roleId);
}
