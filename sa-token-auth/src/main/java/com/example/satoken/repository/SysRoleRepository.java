package com.example.satoken.repository;

import com.example.satoken.entity.SysRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 角色数据访问层
 * 
 * <AUTHOR> Team
 */
@Repository
public interface SysRoleRepository extends JpaRepository<SysRole, Long> {

    /**
     * 根据角色编码查找角色
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Optional<SysRole> findByRoleCode(String roleCode);

    /**
     * 根据角色编码查找角色（包含权限信息）
     * @param roleCode 角色编码
     * @return 角色信息
     */
    @Query("SELECT r FROM SysRole r " +
           "LEFT JOIN FETCH r.permissions p " +
           "WHERE r.roleCode = :roleCode")
    Optional<SysRole> findByRoleCodeWithPermissions(@Param("roleCode") String roleCode);

    /**
     * 查找所有启用的角色
     * @return 角色列表
     */
    List<SysRole> findByStatus(Integer status);

    /**
     * 检查角色编码是否存在
     * @param roleCode 角色编码
     * @return 是否存在
     */
    boolean existsByRoleCode(String roleCode);

    /**
     * 根据用户ID查找角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    @Query("SELECT r FROM SysRole r " +
           "JOIN r.users u " +
           "WHERE u.id = :userId AND r.status = 1")
    List<SysRole> findByUserId(@Param("userId") Long userId);
}
