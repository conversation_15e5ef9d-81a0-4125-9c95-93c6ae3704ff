package com.example.satoken.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.satoken.dto.LoginRequest;
import com.example.satoken.dto.LoginResponse;
import com.example.satoken.dto.RegisterRequest;
import com.example.satoken.dto.Result;
import com.example.satoken.entity.SysUser;
import com.example.satoken.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse response = authService.login(loginRequest);
            return Result.success("登录成功", response);
        } catch (Exception e) {
            log.error("Login failed for user: {}", loginRequest.getUsername(), e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册响应
     */
    @PostMapping("/register")
    public Result<String> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            SysUser user = authService.register(registerRequest);
            return Result.success("注册成功", "用户ID: " + user.getId());
        } catch (Exception e) {
            log.error("Registration failed for user: {}", registerRequest.getUsername(), e);
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     * @return 登出响应
     */
    @PostMapping("/logout")
    public Result<String> logout() {
        try {
            authService.logout();
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("Logout failed", e);
            return Result.error("登出失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    @GetMapping("/me")
    public Result<LoginResponse.UserInfo> getCurrentUser() {
        try {
            LoginResponse.UserInfo userInfo = authService.getCurrentUserInfo();
            return Result.success("获取用户信息成功", userInfo);
        } catch (Exception e) {
            log.error("Get current user failed", e);
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 检查登录状态
     * @return 登录状态
     */
    @GetMapping("/check")
    public Result<Object> checkLogin() {
        try {
            boolean isLogin = StpUtil.isLogin();
            if (isLogin) {
                Object loginId = StpUtil.getLoginId();
                return Result.success("用户已登录", "用户ID: " + loginId);
            } else {
                return Result.unauthorized("用户未登录");
            }
        } catch (Exception e) {
            log.error("Check login failed", e);
            return Result.error("检查登录状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取Token信息
     * @return Token信息
     */
    @GetMapping("/token-info")
    public Result<Object> getTokenInfo() {
        try {
            if (StpUtil.isLogin()) {
                return Result.success("获取Token信息成功", StpUtil.getTokenInfo());
            } else {
                return Result.unauthorized("用户未登录");
            }
        } catch (Exception e) {
            log.error("Get token info failed", e);
            return Result.error("获取Token信息失败：" + e.getMessage());
        }
    }
}
