package com.example.satoken.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.example.satoken.dto.Result;
import com.example.satoken.entity.SysUser;
import com.example.satoken.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

/**
 * 用户控制器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 分页查询用户
     * @param page 页码
     * @param size 页大小
     * @return 用户分页数据
     */
    @GetMapping
    @SaCheckRole("ADMIN")
    @SaCheckPermission("user:read")
    public Result<Page<SysUser>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<SysUser> users = userService.findUsers(pageable);
            return Result.success("查询用户列表成功", users);
        } catch (Exception e) {
            log.error("Get users failed", e);
            return Result.error("查询用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    public Result<SysUser> getUserById(@PathVariable Long id) {
        try {
            // 检查权限：管理员或者查看自己的信息
            if (!StpUtil.hasRole("ADMIN") && !id.equals(Long.valueOf(StpUtil.getLoginId().toString()))) {
                return Result.forbidden("权限不足");
            }
            
            Optional<SysUser> user = userService.findById(id);
            if (user.isPresent()) {
                return Result.success("查询用户成功", user.get());
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            log.error("Get user by id failed: {}", id, e);
            return Result.error("查询用户失败：" + e.getMessage());
        }
    }

    /**
     * 创建用户
     * @param user 用户信息
     * @return 创建结果
     */
    @PostMapping
    @SaCheckRole("ADMIN")
    @SaCheckPermission("user:create")
    public Result<SysUser> createUser(@RequestBody SysUser user) {
        try {
            SysUser createdUser = userService.createUser(user);
            return Result.success("创建用户成功", createdUser);
        } catch (Exception e) {
            log.error("Create user failed", e);
            return Result.error("创建用户失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户
     * @param id 用户ID
     * @param user 用户信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Result<SysUser> updateUser(@PathVariable Long id, @RequestBody SysUser user) {
        try {
            // 检查权限：管理员或者修改自己的信息
            if (!StpUtil.hasRole("ADMIN") && !id.equals(Long.valueOf(StpUtil.getLoginId().toString()))) {
                return Result.forbidden("权限不足");
            }
            
            user.setId(id);
            SysUser updatedUser = userService.updateUser(user);
            return Result.success("更新用户成功", updatedUser);
        } catch (Exception e) {
            log.error("Update user failed: {}", id, e);
            return Result.error("更新用户失败：" + e.getMessage());
        }
    }

    /**
     * 修改密码
     * @param id 用户ID
     * @param passwordData 密码数据
     * @return 修改结果
     */
    @PutMapping("/{id}/password")
    public Result<String> changePassword(@PathVariable Long id, @RequestBody Map<String, String> passwordData) {
        try {
            // 检查权限：管理员或者修改自己的密码
            if (!StpUtil.hasRole("ADMIN") && !id.equals(Long.valueOf(StpUtil.getLoginId().toString()))) {
                return Result.forbidden("权限不足");
            }
            
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");
            
            userService.changePassword(id, oldPassword, newPassword);
            return Result.success("修改密码成功");
        } catch (Exception e) {
            log.error("Change password failed: {}", id, e);
            return Result.error("修改密码失败：" + e.getMessage());
        }
    }

    /**
     * 重置密码（管理员功能）
     * @param id 用户ID
     * @param passwordData 密码数据
     * @return 重置结果
     */
    @PutMapping("/{id}/reset-password")
    @SaCheckRole("ADMIN")
    @SaCheckPermission("user:reset-password")
    public Result<String> resetPassword(@PathVariable Long id, @RequestBody Map<String, String> passwordData) {
        try {
            String newPassword = passwordData.get("newPassword");
            userService.resetPassword(id, newPassword);
            return Result.success("重置密码成功");
        } catch (Exception e) {
            log.error("Reset password failed: {}", id, e);
            return Result.error("重置密码失败：" + e.getMessage());
        }
    }

    /**
     * 启用/禁用用户
     * @param id 用户ID
     * @param statusData 状态数据
     * @return 更新结果
     */
    @PutMapping("/{id}/status")
    @SaCheckRole("ADMIN")
    @SaCheckPermission("user:update-status")
    public Result<String> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> statusData) {
        try {
            Integer status = statusData.get("status");
            userService.updateUserStatus(id, status);
            return Result.success("更新用户状态成功");
        } catch (Exception e) {
            log.error("Update user status failed: {}", id, e);
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("ADMIN")
    @SaCheckPermission("user:delete")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return Result.success("删除用户成功");
        } catch (Exception e) {
            log.error("Delete user failed: {}", id, e);
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }
}
