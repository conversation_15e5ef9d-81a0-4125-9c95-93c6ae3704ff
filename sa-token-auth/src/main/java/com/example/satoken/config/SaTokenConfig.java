package com.example.satoken.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 * 
 * <AUTHOR> Team
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/auth/**",           // 认证相关接口
                    "/public/**",         // 公开接口
                    "/actuator/**",       // 监控接口
                    "/swagger-ui/**",     // Swagger文档
                    "/v3/api-docs/**",    // API文档
                    "/error",             // 错误页面
                    "/",                  // 首页
                    "/index.html",        // 首页
                    "/css/**",            // CSS文件
                    "/js/**",             // JS文件
                    "/images/**"          // 图片文件
                );
    }

    /**
     * Sa-Token 整合 JWT (Simple 简单模式)
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }
}
