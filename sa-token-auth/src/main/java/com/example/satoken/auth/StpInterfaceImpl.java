package com.example.satoken.auth;

import cn.dev33.satoken.stp.StpInterface;
import com.example.satoken.entity.SysPermission;
import com.example.satoken.entity.SysRole;
import com.example.satoken.repository.SysPermissionRepository;
import com.example.satoken.repository.SysRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义权限验证接口扩展
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {

    private final SysRoleRepository roleRepository;
    private final SysPermissionRepository permissionRepository;

    /**
     * 返回一个账号所拥有的权限码集合
     * @param loginId 账号id
     * @param loginType 账号类型
     * @return 权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.debug("Getting permissions for user: {}", loginId);
        
        try {
            Long userId = Long.valueOf(loginId.toString());
            List<SysPermission> permissions = permissionRepository.findByUserId(userId);
            
            List<String> permissionCodes = permissions.stream()
                    .map(SysPermission::getPermissionCode)
                    .collect(Collectors.toList());
            
            log.debug("User {} has permissions: {}", userId, permissionCodes);
            return permissionCodes;
        } catch (Exception e) {
            log.error("Error getting permissions for user: {}", loginId, e);
            return List.of();
        }
    }

    /**
     * 返回一个账号所拥有的角色标识集合
     * @param loginId 账号id
     * @param loginType 账号类型
     * @return 角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        log.debug("Getting roles for user: {}", loginId);
        
        try {
            Long userId = Long.valueOf(loginId.toString());
            List<SysRole> roles = roleRepository.findByUserId(userId);
            
            List<String> roleCodes = roles.stream()
                    .map(SysRole::getRoleCode)
                    .collect(Collectors.toList());
            
            log.debug("User {} has roles: {}", userId, roleCodes);
            return roleCodes;
        } catch (Exception e) {
            log.error("Error getting roles for user: {}", loginId, e);
            return List.of();
        }
    }
}
