# Sa-Token 权限控制项目

## 项目简介

本项目是一个基于 Sa-Token + JWT 的用户权限控制系统，实现了完整的 RBAC（基于角色的访问控制）权限模型。Sa-Token 是一个轻量级 Java 权限认证框架，提供了更简单易用的 API。

## 技术栈

- **Spring Boot 3.1.0** - 主框架
- **Sa-Token 1.37.0** - 权限认证框架
- **Sa-Token JWT** - JWT 集成
- **Spring Data JPA** - 数据访问层
- **MySQL 8.0** - 数据库
- **Lombok** - 简化代码
- **Maven** - 项目构建

## 功能特性

### 认证功能
- ✅ 用户注册
- ✅ 用户登录
- ✅ JWT Token 生成和验证
- ✅ 用户登出
- ✅ 获取当前用户信息
- ✅ 登录状态检查
- ✅ Token 信息查询

### 权限控制
- ✅ 基于角色的访问控制 (RBAC)
- ✅ 注解式权限控制 (@SaCheckRole, @SaCheckPermission)
- ✅ 编程式权限验证
- ✅ 动态权限验证

### 用户管理
- ✅ 用户 CRUD 操作
- ✅ 密码修改和重置
- ✅ 用户状态管理（启用/禁用）
- ✅ 分页查询

## Sa-Token 特色功能

### 1. 简单易用的 API
```java
// 登录
StpUtil.login(userId);

// 检查登录
StpUtil.checkLogin();

// 检查权限
StpUtil.checkPermission("user:read");

// 检查角色
StpUtil.checkRole("ADMIN");

// 登出
StpUtil.logout();
```

### 2. 注解式权限控制
```java
@SaCheckRole("ADMIN")                    // 检查角色
@SaCheckPermission("user:read")          // 检查权限
@SaCheckLogin                            // 检查登录
```

### 3. JWT 集成
- 支持 JWT 无状态认证
- 自动 Token 生成和验证
- 灵活的配置选项

## 数据库设计

### 核心表结构
1. **sys_user** - 用户表
2. **sys_role** - 角色表
3. **sys_permission** - 权限表
4. **sys_user_role** - 用户角色关联表
5. **sys_role_permission** - 角色权限关联表

## 快速开始

### 1. 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置
```sql
CREATE DATABASE sa_token_auth CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 修改配置文件
编辑 `src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
```

### 4. 启动项目
```bash
mvn spring-boot:run
```

### 5. 访问地址
- 应用地址：http://localhost:8082/sa-token-auth
- 数据库表会自动创建，初始数据会自动插入

## 默认账号

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | ADMIN | 管理员账号，拥有所有权限 |
| testuser | test123 | USER | 测试账号，拥有基本权限 |

## API 接口

### 认证接口
```http
POST /auth/login          # 用户登录
POST /auth/register       # 用户注册
POST /auth/logout         # 用户登出
GET  /auth/me            # 获取当前用户信息
GET  /auth/check         # 检查登录状态
GET  /auth/token-info    # 获取Token信息
```

### 用户管理接口
```http
GET    /api/users              # 查询用户列表（需要权限）
GET    /api/users/{id}         # 查询用户详情
POST   /api/users              # 创建用户（需要权限）
PUT    /api/users/{id}         # 更新用户
PUT    /api/users/{id}/password # 修改密码
PUT    /api/users/{id}/reset-password # 重置密码（管理员）
PUT    /api/users/{id}/status  # 更新用户状态（管理员）
DELETE /api/users/{id}         # 删除用户（管理员）
```

## 使用示例

### 1. 用户登录
```bash
curl -X POST http://localhost:8082/sa-token-auth/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 检查登录状态
```bash
curl -X GET http://localhost:8082/sa-token-auth/auth/check \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 访问受保护的接口
```bash
curl -X GET http://localhost:8082/sa-token-auth/api/users \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Sa-Token 配置

### 基础配置
```yaml
sa-token:
  token-name: satoken              # token名称
  timeout: 86400                   # token有效期（秒）
  active-timeout: -1               # token最低活跃频率
  is-concurrent: true              # 是否允许同一账号并发登录
  is-share: true                   # 多人登录是否共用token
  token-style: uuid                # token风格
  is-log: true                     # 是否输出操作日志
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz  # JWT密钥
```

## 权限配置

### 角色权限
- **ADMIN**: 拥有所有权限
- **USER**: 拥有基本查看权限

### 权限编码
- `user:read` - 查看用户
- `user:create` - 创建用户
- `user:update` - 更新用户
- `user:delete` - 删除用户
- `user:reset-password` - 重置密码
- `user:update-status` - 更新用户状态

## Sa-Token vs Spring Security

| 特性 | Sa-Token | Spring Security |
|------|----------|-----------------|
| 学习成本 | 低 | 高 |
| API 简洁性 | 简洁直观 | 相对复杂 |
| 配置复杂度 | 简单 | 复杂 |
| 功能完整性 | 完整 | 非常完整 |
| 社区生态 | 较新 | 成熟 |
| 文档质量 | 优秀 | 优秀 |

## 项目结构

```
src/main/java/com/example/satoken/
├── auth/           # 权限验证
├── config/         # 配置类
├── controller/     # 控制器
├── dto/           # 数据传输对象
├── entity/        # 实体类
├── repository/    # 数据访问层
└── service/       # 业务逻辑层
```

## 开发说明

### 添加新的权限
1. 在 `DataInitializer` 中添加权限定义
2. 在对应的 Controller 方法上添加 `@SaCheckPermission` 注解
3. 重启应用，权限会自动初始化

### 自定义权限验证
实现 `StpInterface` 接口：
```java
@Component
public class StpInterfaceImpl implements StpInterface {
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        // 返回用户权限列表
    }
    
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        // 返回用户角色列表
    }
}
```

## 注意事项

1. Sa-Token 配置中的 JWT 密钥在生产环境中应该使用更复杂的密钥
2. 数据库连接信息不要提交到版本控制系统
3. 建议在生产环境中启用 HTTPS
4. Sa-Token 提供了丰富的配置选项，可根据需要调整

## 相关链接

- [Sa-Token 官方文档](https://sa-token.cc/)
- [Sa-Token GitHub](https://github.com/dromara/Sa-Token)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
