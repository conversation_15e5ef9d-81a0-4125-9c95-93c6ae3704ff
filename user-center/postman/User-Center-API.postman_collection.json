{"info": {"name": "User Center API", "description": "用户中心 API 接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8084", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/actuator/health", "host": ["{{baseUrl}}"], "path": ["actuator", "health"]}}}, {"name": "用户注册", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success) {", "        console.log('用户注册成功:', response.data);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser001\",\n  \"password\": \"123456\",\n  \"nickname\": \"测试用户001\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/register", "host": ["{{baseUrl}}"], "path": ["api", "users", "register"]}}}, {"name": "用户登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('token', response.data);", "        console.log('登录成功，Token已保存');", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "testuser001"}, {"key": "password", "value": "123456"}]}, "url": {"raw": "{{baseUrl}}/api/users/login", "host": ["{{baseUrl}}"], "path": ["api", "users", "login"]}}}, {"name": "Token验证", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/validate", "host": ["{{baseUrl}}"], "path": ["api", "users", "validate"]}}}, {"name": "获取用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/1", "host": ["{{baseUrl}}"], "path": ["api", "users", "1"]}}}, {"name": "更新用户信息", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser001\",\n  \"nickname\": \"更新后的昵称\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/1", "host": ["{{baseUrl}}"], "path": ["api", "users", "1"]}}}, {"name": "删除用户", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/1", "host": ["{{baseUrl}}"], "path": ["api", "users", "1"]}}}]}