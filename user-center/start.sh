#!/bin/bash

# 用户中心快速启动脚本

echo "=== 用户中心启动脚本 ==="

# 检查 Java 版本
echo "检查 Java 版本..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到 Java，请确保已安装 JDK 17+"
    exit 1
fi

# 检查 Maven
echo "检查 Maven..."
mvn -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到 Maven，请确保已安装 Maven 3.6+"
    exit 1
fi

# 检查 MySQL 连接
echo "检查 MySQL 连接..."
mysql -h localhost -u root -p123456 -e "SELECT 1;" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法连接到 MySQL，请确保:"
    echo "1. MySQL 服务已启动"
    echo "2. 数据库连接配置正确"
    echo "3. 已创建 user_center 数据库"
    echo ""
    echo "创建数据库命令:"
    echo "CREATE DATABASE user_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo ""
    read -p "是否继续启动? (y/n): " continue_start
    if [ "$continue_start" != "y" ]; then
        exit 1
    fi
fi

# 编译项目
echo "编译项目..."
cd ..
mvn clean compile -pl user-center-api,user-center
if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

# 启动服务
echo "启动用户中心服务..."
cd user-center
mvn spring-boot:run &

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
for i in {1..30}; do
    curl -s http://localhost:8084/actuator/health > /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 服务启动成功!"
        echo ""
        echo "🌐 服务地址: http://localhost:8084"
        echo "📚 API文档: http://localhost:8084/swagger-ui/index.html"
        echo "💚 健康检查: http://localhost:8084/actuator/health"
        echo ""
        echo "🧪 运行测试:"
        echo "  ./test-scripts/api-test.sh"
        echo ""
        echo "📋 导入 Postman 集合:"
        echo "  ./postman/User-Center-API.postman_collection.json"
        exit 0
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

echo "❌ 服务启动失败或超时"
echo "请检查日志: logs/user-center/"
exit 1
