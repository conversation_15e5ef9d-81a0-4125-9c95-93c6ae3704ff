#!/bin/bash

# 用户中心 API 测试脚本
# 使用方法: ./api-test.sh

BASE_URL="http://localhost:8084"
CONTENT_TYPE="Content-Type: application/json"

echo "=== 用户中心 API 测试 ==="
echo "Base URL: $BASE_URL"
echo

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/actuator/health" | jq '.'
echo -e "\n"

# 2. 用户注册
echo "2. 用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/users/register" \
  -H "$CONTENT_TYPE" \
  -d '{
    "username": "testuser001",
    "password": "123456",
    "nickname": "测试用户001",
    "email": "<EMAIL>"
  }')

echo "$REGISTER_RESPONSE" | jq '.'
echo -e "\n"

# 3. 用户登录
echo "3. 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/users/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser001&password=123456")

echo "$LOGIN_RESPONSE" | jq '.'

# 提取 JWT Token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data')
echo "JWT Token: $TOKEN"
echo -e "\n"

if [ "$TOKEN" != "null" ] && [ "$TOKEN" != "" ]; then
    # 4. Token 验证
    echo "4. Token 验证..."
    curl -s -X GET "$BASE_URL/api/users/validate" \
      -H "Authorization: Bearer $TOKEN" | jq '.'
    echo -e "\n"

    # 5. 获取用户信息 (假设用户ID为1)
    echo "5. 获取用户信息..."
    curl -s -X GET "$BASE_URL/api/users/1" \
      -H "Authorization: Bearer $TOKEN" | jq '.'
    echo -e "\n"

    # 6. 更新用户信息
    echo "6. 更新用户信息..."
    curl -s -X PUT "$BASE_URL/api/users/1" \
      -H "Authorization: Bearer $TOKEN" \
      -H "$CONTENT_TYPE" \
      -d '{
        "username": "testuser001",
        "nickname": "更新后的昵称",
        "email": "<EMAIL>"
      }' | jq '.'
    echo -e "\n"

else
    echo "登录失败，无法获取 Token，跳过需要认证的测试"
fi

echo "=== 测试完成 ==="
