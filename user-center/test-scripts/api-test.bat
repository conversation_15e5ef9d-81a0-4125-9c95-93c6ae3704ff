@echo off
REM 用户中心 API 测试脚本 (Windows)
REM 使用方法: api-test.bat

set BASE_URL=http://localhost:8084
set CONTENT_TYPE=Content-Type: application/json

echo === 用户中心 API 测试 ===
echo Base URL: %BASE_URL%
echo.

REM 1. 健康检查
echo 1. 健康检查...
curl -s "%BASE_URL%/actuator/health"
echo.
echo.

REM 2. 用户注册
echo 2. 用户注册...
curl -s -X POST "%BASE_URL%/api/users/register" ^
  -H "%CONTENT_TYPE%" ^
  -d "{\"username\": \"testuser001\", \"password\": \"123456\", \"nickname\": \"测试用户001\", \"email\": \"<EMAIL>\"}"
echo.
echo.

REM 3. 用户登录
echo 3. 用户登录...
curl -s -X POST "%BASE_URL%/api/users/login" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "username=testuser001&password=123456" > login_response.json

type login_response.json
echo.

REM 注意: Windows 批处理中提取 JSON 比较复杂，建议手动复制 token 进行后续测试
echo 请手动复制上面返回的 token，然后使用以下命令进行后续测试:
echo.
echo 4. Token 验证:
echo curl -s -X GET "%BASE_URL%/api/users/validate" -H "Authorization: Bearer YOUR_TOKEN"
echo.
echo 5. 获取用户信息:
echo curl -s -X GET "%BASE_URL%/api/users/1" -H "Authorization: Bearer YOUR_TOKEN"
echo.
echo 6. 更新用户信息:
echo curl -s -X PUT "%BASE_URL%/api/users/1" -H "Authorization: Bearer YOUR_TOKEN" -H "%CONTENT_TYPE%" -d "{\"username\": \"testuser001\", \"nickname\": \"更新后的昵称\", \"email\": \"<EMAIL>\"}"

echo.
echo === 测试完成 ===
pause
