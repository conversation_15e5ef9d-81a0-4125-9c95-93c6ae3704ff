package com.example.user.exception;

import com.example.user.constant.UserConstants;
import lombok.Getter;

/**
 * 用户相关异常类
 * 统一使用 UserConstants 中的错误码和消息
 */
@Getter
public class UserException extends BaseException {

    public UserException(String errorCode, String message) {
        super(errorCode, message);
    }

    public static UserException userNotFound() {
        return new UserException(UserConstants.ERROR_USER_NOT_FOUND, UserConstants.MSG_USER_NOT_FOUND);
    }

    public static UserException userExists() {
        return new UserException(UserConstants.ERROR_USER_EXISTS, UserConstants.MSG_USERNAME_EXISTS);
    }

    public static UserException invalidPassword() {
        return new UserException(UserConstants.ERROR_INVALID_PASSWORD, UserConstants.MSG_PASSWORD_INCORRECT);
    }

    public static UserException invalidToken() {
        return new UserException(UserConstants.ERROR_INVALID_TOKEN, UserConstants.MSG_TOKEN_INVALID);
    }

    public static UserException tokenExpired() {
        return new UserException(UserConstants.ERROR_TOKEN_EXPIRED, UserConstants.MSG_TOKEN_EXPIRED);
    }
}