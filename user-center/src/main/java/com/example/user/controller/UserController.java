package com.example.user.controller;


import com.example.common.model.Result;
import com.example.user.api.UserApi;
import com.example.user.dto.UserDTO;
import com.example.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
public class UserController implements UserApi {
    private final UserService userService;

    @Override
    public Result<UserDTO> register(@RequestBody UserDTO userDTO) {
        log.debug("接收注册请求: {}", userDTO.getUsername());
        return userService.register(userDTO);
    }

    @Override
    public Result<String> login(@RequestParam String username, @RequestParam String password) {
        log.debug("接收登录请求: {}", username);
        return userService.login(username, password);
    }

    @Override
    public Result<UserDTO> validateToken(@RequestHeader("Authorization") String token) {
        log.debug("接收token验证请求");
        return userService.validateToken(token);
    }

    @Override
    public Result<UserDTO> update(@PathVariable Long id, @RequestBody UserDTO userDTO) {
        log.debug("接收更新请求: id={}", id);
        return userService.update(id, userDTO);
    }

    @Override
    public Result<Void> delete(@PathVariable Long id) {
        log.debug("接收删除请求: id={}", id);
        return userService.delete(id);
    }

    @Override
    public Result<UserDTO> findById(@PathVariable Long id) {
        log.debug("接收查询请求: id={}", id);
        return userService.findById(id);
    }
} 