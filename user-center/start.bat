@echo off
REM 用户中心快速启动脚本 (Windows)

echo === 用户中心启动脚本 ===

REM 检查 Java 版本
echo 检查 Java 版本...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Java，请确保已安装 JDK 17+
    pause
    exit /b 1
)

REM 检查 Maven
echo 检查 Maven...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Maven，请确保已安装 Maven 3.6+
    pause
    exit /b 1
)

REM 编译项目
echo 编译项目...
cd ..
mvn clean compile -pl user-center-api,user-center
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

REM 启动服务
echo 启动用户中心服务...
cd user-center
start "User Center" mvn spring-boot:run

REM 等待服务启动
echo 等待服务启动...
timeout /t 15 /nobreak >nul

REM 检查服务状态
echo 检查服务状态...
curl -s http://localhost:8084/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 服务启动成功!
    echo.
    echo 🌐 服务地址: http://localhost:8084
    echo 📚 API文档: http://localhost:8084/swagger-ui/index.html
    echo 💚 健康检查: http://localhost:8084/actuator/health
    echo.
    echo 🧪 运行测试:
    echo   test-scripts\api-test.bat
    echo.
    echo 📋 导入 Postman 集合:
    echo   postman\User-Center-API.postman_collection.json
) else (
    echo ❌ 服务可能还在启动中，请稍等片刻后访问:
    echo http://localhost:8084/actuator/health
)

echo.
echo 按任意键退出...
pause >nul
