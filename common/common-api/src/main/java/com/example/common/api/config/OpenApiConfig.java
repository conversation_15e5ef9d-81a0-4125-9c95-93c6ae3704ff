package com.example.common.api.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI 配置
 * 提供通用的 Swagger 文档配置
 * 
 * <AUTHOR> Team
 */
@Configuration
@ConditionalOnProperty(name = "common.openapi.enabled", havingValue = "true", matchIfMissing = true)
public class OpenApiConfig {
    
    @Value("${common.openapi.title:Spring Boot API}")
    private String title;
    
    @Value("${common.openapi.description:Spring Boot API 文档}")
    private String description;
    
    @Value("${common.openapi.version:v1.0.0}")
    private String version;
    
    @Value("${common.openapi.contact.name:开发团队}")
    private String contactName;
    
    @Value("${common.openapi.contact.email:<EMAIL>}")
    private String contactEmail;
    
    @Value("${common.openapi.contact.url:https://example.com}")
    private String contactUrl;
    
    @Value("${server.port:8080}")
    private String serverPort;
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title(title)
                .description(buildDescription())
                .version(version)
                .contact(new Contact()
                    .name(contactName)
                    .email(contactEmail)
                    .url(contactUrl))
                .license(new License()
                    .name("Apache 2.0")
                    .url("https://www.apache.org/licenses/LICENSE-2.0")))
            .servers(List.of(
                new Server()
                    .url("http://localhost:" + serverPort)
                    .description("本地开发环境"),
                new Server()
                    .url("https://api.example.com")
                    .description("生产环境")
            ))
            .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
            .components(new Components()
                .addSecuritySchemes("Bearer Authentication", 
                    new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .description("请输入 JWT Token，格式：Bearer {token}")
                )
            );
    }
    
    /**
     * 构建 API 描述
     */
    private String buildDescription() {
        return String.format("""
            ## %s
            
            ### 功能概述
            提供完整的业务功能，包括：
            - 🔐 用户认证与授权
            - 🎫 JWT Token 认证
            - 📊 数据管理
            - 🔒 权限控制
            
            ### 认证说明
            除了公开接口外，其他接口都需要在请求头中携带 JWT Token：
            ```
            Authorization: Bearer {your-jwt-token}
            ```
            
            ### 响应格式
            所有接口都使用统一的响应格式：
            ```json
            {
              "success": true,
              "code": "200",
              "message": "操作成功",
              "data": {...}
            }
            ```
            
            ### 错误码说明
            - `200`: 操作成功
            - `400`: 参数错误
            - `401`: 认证失败
            - `403`: 权限不足
            - `404`: 资源不存在
            - `500`: 服务器内部错误
            """, description);
    }
}
