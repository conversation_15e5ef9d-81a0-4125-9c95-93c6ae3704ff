package com.example.common.exceptions;

/**
 * 权限不足异常
 * 用于处理权限相关的异常
 * 
 * <AUTHOR> Team
 */
public class PermissionDeniedException extends BaseException {
    
    public PermissionDeniedException(String code, String message) {
        super(code, message);
    }
    
    public PermissionDeniedException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 创建权限不足异常
     * @param operation 操作名称
     * @return 权限不足异常
     */
    public static PermissionDeniedException of(String operation) {
        return new PermissionDeniedException("PERMISSION_DENIED", 
            "权限不足，无法执行操作：" + operation);
    }
    
    /**
     * 创建权限不足异常
     * @return 权限不足异常
     */
    public static PermissionDeniedException of() {
        return new PermissionDeniedException("PERMISSION_DENIED", "权限不足");
    }
}
