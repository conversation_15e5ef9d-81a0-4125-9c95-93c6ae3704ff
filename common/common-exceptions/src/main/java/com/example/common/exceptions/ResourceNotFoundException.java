package com.example.common.exceptions;

/**
 * 资源不存在异常
 * 用于处理资源未找到的情况
 * 
 * <AUTHOR> Team
 */
public class ResourceNotFoundException extends BaseException {
    
    public ResourceNotFoundException(String code, String message) {
        super(code, message);
    }
    
    public ResourceNotFoundException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 创建资源不存在异常
     * @param resource 资源名称
     * @return 资源不存在异常
     */
    public static ResourceNotFoundException of(String resource) {
        return new ResourceNotFoundException("RESOURCE_NOT_FOUND", resource + "不存在");
    }
    
    /**
     * 创建资源不存在异常
     * @param resource 资源名称
     * @param id 资源ID
     * @return 资源不存在异常
     */
    public static ResourceNotFoundException of(String resource, Object id) {
        return new ResourceNotFoundException("RESOURCE_NOT_FOUND", 
            String.format("%s[%s]不存在", resource, id));
    }
}
