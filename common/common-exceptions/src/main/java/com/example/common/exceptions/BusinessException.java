package com.example.common.exceptions;

/**
 * 业务异常
 * 用于处理业务逻辑相关的异常
 * 
 * <AUTHOR> Team
 */
public class BusinessException extends BaseException {
    
    public BusinessException(String code, String message) {
        super(code, message);
    }
    
    public BusinessException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 创建业务异常
     * @param message 错误消息
     * @return 业务异常
     */
    public static BusinessException of(String message) {
        return new BusinessException("BUSINESS_ERROR", message);
    }
    
    /**
     * 创建业务异常
     * @param code 错误码
     * @param message 错误消息
     * @return 业务异常
     */
    public static BusinessException of(String code, String message) {
        return new BusinessException(code, message);
    }
}
