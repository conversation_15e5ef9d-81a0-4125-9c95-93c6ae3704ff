package com.example.common.exceptions;

import lombok.Getter;

/**
 * 基础异常类
 * 所有业务异常都应该继承此类
 * 
 * <AUTHOR> Team
 */
@Getter
public abstract class BaseException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final String code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    /**
     * 构造函数
     * @param code 错误码
     * @param message 错误消息
     */
    public BaseException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原因
     */
    public BaseException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
}
