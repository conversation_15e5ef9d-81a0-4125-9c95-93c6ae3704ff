package com.example.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置
 * 提供通用的缓存管理配置
 * 
 * <AUTHOR> Team
 */
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "common.cache.enabled", havingValue = "true", matchIfMissing = true)
public class CacheConfig {
    
    /**
     * 缓存管理器
     * 默认使用内存缓存，生产环境建议使用 Redis
     */
    @Bean
    @ConditionalOnProperty(name = "common.cache.type", havingValue = "memory", matchIfMissing = true)
    public CacheManager memoryCacheManager() {
        return new ConcurrentMapCacheManager(
            "users", "roles", "permissions", 
            "configs", "dictionaries", "menus"
        );
    }
}
