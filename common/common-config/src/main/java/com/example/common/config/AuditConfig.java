package com.example.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

/**
 * 审计配置
 * 提供 JPA 审计功能，自动记录创建人、修改人等信息
 * 
 * <AUTHOR> Team
 */
@Configuration
@EnableJpaAuditing
@ConditionalOnProperty(name = "common.audit.enabled", havingValue = "true", matchIfMissing = true)
public class AuditConfig {
    
    /**
     * 审计员提供者
     * 从 Spring Security 上下文中获取当前用户
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return Optional.of("system");
            }
            
            // 如果是匿名用户，返回 system
            if ("anonymousUser".equals(authentication.getName())) {
                return Optional.of("system");
            }
            
            return Optional.of(authentication.getName());
        };
    }
}
