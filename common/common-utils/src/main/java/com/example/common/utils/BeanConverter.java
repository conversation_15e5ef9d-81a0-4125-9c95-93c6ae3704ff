package com.example.common.utils;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * Bean 转换工具类
 * 提供对象之间的转换功能
 * 
 * <AUTHOR> Team
 */
public class BeanConverter {
    
    /**
     * 单个对象转换
     * @param source 源对象
     * @param targetSupplier 目标对象供应商
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <S, T> T convert(S source, Supplier<T> targetSupplier) {
        if (source == null) {
            return null;
        }
        T target = targetSupplier.get();
        BeanUtils.copyProperties(source, target);
        return target;
    }
    
    /**
     * 列表对象转换
     * @param sources 源对象列表
     * @param targetSupplier 目标对象供应商
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 转换后的对象列表
     */
    public static <S, T> List<T> convertList(List<S> sources, Supplier<T> targetSupplier) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<T> targets = new ArrayList<>(sources.size());
        for (S source : sources) {
            T target = convert(source, targetSupplier);
            if (target != null) {
                targets.add(target);
            }
        }
        return targets;
    }
    
    /**
     * 单个对象转换（指定目标类型）
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <S, T> T convert(S source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("对象转换失败", e);
        }
    }
    
    /**
     * 列表对象转换（指定目标类型）
     * @param sources 源对象列表
     * @param targetClass 目标类型
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 转换后的对象列表
     */
    public static <S, T> List<T> convertList(List<S> sources, Class<T> targetClass) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<T> targets = new ArrayList<>(sources.size());
        for (S source : sources) {
            T target = convert(source, targetClass);
            if (target != null) {
                targets.add(target);
            }
        }
        return targets;
    }
}
