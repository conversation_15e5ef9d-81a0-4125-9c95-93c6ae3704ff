package com.learning.designpatterns.behavioral.mediator;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 具体聊天室中介者
 * 
 * 实现聊天室的具体功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class ConcreteChatRoom implements ChatRoomMediator {
    
    private final String roomName;
    private final List<User> users;
    
    public ConcreteChatRoom(String roomName) {
        this.roomName = roomName;
        this.users = new ArrayList<>();
    }
    
    @Override
    public void addUser(User user) {
        users.add(user);
        log.info("✅ {} 加入了聊天室 '{}'", user.getName(), roomName);
        
        // 通知其他用户
        for (User existingUser : users) {
            if (!existingUser.equals(user)) {
                existingUser.receiveMessage(user.getName() + " 加入了聊天室", "系统消息");
            }
        }
    }
    
    @Override
    public void removeUser(User user) {
        if (users.remove(user)) {
            log.info("👋 {} 离开了聊天室 '{}'", user.getName(), roomName);
            
            // 通知其他用户
            for (User remainingUser : users) {
                remainingUser.receiveMessage(user.getName() + " 离开了聊天室", "系统消息");
            }
        }
    }
    
    @Override
    public void sendMessage(String message, User sender) {
        log.info("📢 在聊天室 '{}' 中广播消息", roomName);
        
        // 发送给除发送者外的所有用户
        for (User user : users) {
            if (!user.equals(sender)) {
                user.receiveMessage(message, sender.getName());
            }
        }
    }
    
    @Override
    public void sendPrivateMessage(String message, User sender, String receiverName) {
        User receiver = findUserByName(receiverName);
        
        if (receiver != null) {
            log.info("🔒 在聊天室 '{}' 中转发私聊消息", roomName);
            receiver.receivePrivateMessage(message, sender.getName());
        } else {
            log.warn("⚠️ 用户 '{}' 不在聊天室中", receiverName);
        }
    }
    
    @Override
    public void showOnlineUsers() {
        log.info("👥 聊天室 '{}' 在线用户 ({} 人):", roomName, users.size());
        for (User user : users) {
            log.info("   • {}", user.getName());
        }
    }
    
    /**
     * 根据名称查找用户
     */
    private User findUserByName(String name) {
        return users.stream()
                .filter(user -> user.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}
