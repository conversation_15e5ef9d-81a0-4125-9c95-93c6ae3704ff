package com.learning.designpatterns.behavioral.iterator;

import lombok.Data;
import lombok.AllArgsConstructor;

/**
 * 章节类
 * 
 * 表示书籍中的一个章节
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class Chapter {
    
    /**
     * 章节号
     */
    private int chapterNumber;
    
    /**
     * 章节标题
     */
    private String title;
    
    /**
     * 页数
     */
    private int pageCount;
    
    /**
     * 章节内容摘要
     */
    private String summary;
    
    /**
     * 是否已读
     */
    private boolean isRead;
    
    @Override
    public String toString() {
        return String.format("第%d章: %s (%d页) %s", 
            chapterNumber, title, pageCount, 
            isRead ? "[已读]" : "[未读]");
    }
    
    /**
     * 简化构造函数
     */
    public Chapter(int chapterNumber, String title, int pageCount) {
        this(chapterNumber, title, pageCount, "", false);
    }
    
    /**
     * 标记为已读
     */
    public void markAsRead() {
        this.isRead = true;
    }
    
    /**
     * 标记为未读
     */
    public void markAsUnread() {
        this.isRead = false;
    }
}
