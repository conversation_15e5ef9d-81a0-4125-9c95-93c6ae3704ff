package com.learning.designpatterns.behavioral.iterator;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * 书籍内容类（具体聚合）
 * 
 * 管理书籍的章节内容，支持多种遍历方式
 * 
 * <AUTHOR>
 */
@Slf4j
public class BookContent implements Aggregate<Chapter> {
    
    private final List<Chapter> chapters;
    private final String bookTitle;
    
    /**
     * 构造函数
     * 
     * @param bookTitle 书名
     */
    public BookContent(String bookTitle) {
        this.bookTitle = bookTitle;
        this.chapters = new ArrayList<>();
        log.info("📖 创建书籍内容: {}", bookTitle);
    }
    
    /**
     * 添加章节
     * 
     * @param chapter 章节
     */
    public void addChapter(Chapter chapter) {
        chapters.add(chapter);
        log.info("📝 添加章节到《{}》: {}", bookTitle, chapter.getTitle());
    }
    
    /**
     * 根据章节号获取章节
     * 
     * @param chapterNumber 章节号
     * @return 章节
     */
    public Chapter getChapter(int chapterNumber) {
        return chapters.stream()
            .filter(chapter -> chapter.getChapterNumber() == chapterNumber)
            .findFirst()
            .orElse(null);
    }
    
    @Override
    public Iterator<Chapter> createIterator() {
        log.info("🔄 为《{}》创建顺序迭代器", bookTitle);
        return new SequentialIterator();
    }
    
    /**
     * 创建反向迭代器
     * 
     * @return 反向迭代器
     */
    public Iterator<Chapter> createReverseIterator() {
        log.info("🔄 为《{}》创建反向迭代器", bookTitle);
        return new ReverseIterator();
    }
    
    /**
     * 创建未读章节迭代器
     * 
     * @return 未读章节迭代器
     */
    public Iterator<Chapter> createUnreadIterator() {
        log.info("🔄 为《{}》创建未读章节迭代器", bookTitle);
        return new UnreadIterator();
    }
    
    @Override
    public int size() {
        return chapters.size();
    }
    
    @Override
    public boolean isEmpty() {
        return chapters.isEmpty();
    }
    
    /**
     * 获取书名
     * 
     * @return 书名
     */
    public String getBookTitle() {
        return bookTitle;
    }
    
    /**
     * 显示书籍内容信息
     */
    public void showContentInfo() {
        log.info("📖 书籍内容: {}", bookTitle);
        log.info("   章节总数: {}", chapters.size());
        if (!chapters.isEmpty()) {
            log.info("   章节列表:");
            for (Chapter chapter : chapters) {
                log.info("     {}", chapter);
            }
        }
    }
    
    /**
     * 顺序迭代器
     */
    private class SequentialIterator implements Iterator<Chapter> {
        
        private int currentIndex = 0;
        
        @Override
        public boolean hasNext() {
            return currentIndex < chapters.size();
        }
        
        @Override
        public Chapter next() {
            if (!hasNext()) {
                throw new NoSuchElementException("没有更多章节");
            }
            
            Chapter chapter = chapters.get(currentIndex);
            currentIndex++;
            
            log.info("📖 顺序迭代器返回章节[{}]: {}", currentIndex, chapter.getTitle());
            return chapter;
        }
        
        @Override
        public void reset() {
            currentIndex = 0;
            log.info("🔄 顺序迭代器重置");
        }
        
        @Override
        public int getCurrentPosition() {
            return currentIndex;
        }
    }
    
    /**
     * 反向迭代器
     */
    private class ReverseIterator implements Iterator<Chapter> {
        
        private int currentIndex;
        
        public ReverseIterator() {
            this.currentIndex = chapters.size() - 1;
        }
        
        @Override
        public boolean hasNext() {
            return currentIndex >= 0;
        }
        
        @Override
        public Chapter next() {
            if (!hasNext()) {
                throw new NoSuchElementException("没有更多章节");
            }
            
            Chapter chapter = chapters.get(currentIndex);
            currentIndex--;
            
            log.info("📖 反向迭代器返回章节[{}]: {}", currentIndex + 2, chapter.getTitle());
            return chapter;
        }
        
        @Override
        public void reset() {
            currentIndex = chapters.size() - 1;
            log.info("🔄 反向迭代器重置");
        }
        
        @Override
        public int getCurrentPosition() {
            return chapters.size() - 1 - currentIndex;
        }
    }
    
    /**
     * 未读章节迭代器
     */
    private class UnreadIterator implements Iterator<Chapter> {
        
        private final List<Chapter> unreadChapters;
        private int currentIndex = 0;
        
        public UnreadIterator() {
            this.unreadChapters = new ArrayList<>();
            for (Chapter chapter : chapters) {
                if (!chapter.isRead()) {
                    unreadChapters.add(chapter);
                }
            }
        }
        
        @Override
        public boolean hasNext() {
            return currentIndex < unreadChapters.size();
        }
        
        @Override
        public Chapter next() {
            if (!hasNext()) {
                throw new NoSuchElementException("没有更多未读章节");
            }
            
            Chapter chapter = unreadChapters.get(currentIndex);
            currentIndex++;
            
            log.info("📖 未读迭代器返回章节[{}]: {}", currentIndex, chapter.getTitle());
            return chapter;
        }
        
        @Override
        public void reset() {
            currentIndex = 0;
            log.info("🔄 未读迭代器重置");
        }
        
        @Override
        public int getCurrentPosition() {
            return currentIndex;
        }
    }
}
