package com.learning.designpatterns.behavioral.interpreter;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 上下文类
 * 
 * 包含解释器之外的一些全局信息，如变量的值
 * 
 * <AUTHOR>
 */
@Slf4j
public class Context {
    
    /**
     * 变量存储
     */
    private final Map<String, Integer> variables = new HashMap<>();
    
    /**
     * 设置变量值
     * 
     * @param name 变量名
     * @param value 变量值
     */
    public void setVariable(String name, int value) {
        variables.put(name, value);
        log.info("📝 设置变量: {} = {}", name, value);
    }
    
    /**
     * 获取变量值
     * 
     * @param name 变量名
     * @return 变量值
     * @throws IllegalArgumentException 如果变量不存在
     */
    public int getVariable(String name) {
        if (!variables.containsKey(name)) {
            throw new IllegalArgumentException("变量 '" + name + "' 未定义");
        }
        int value = variables.get(name);
        log.info("📖 读取变量: {} = {}", name, value);
        return value;
    }
    
    /**
     * 检查变量是否存在
     * 
     * @param name 变量名
     * @return 是否存在
     */
    public boolean hasVariable(String name) {
        return variables.containsKey(name);
    }
    
    /**
     * 清空所有变量
     */
    public void clear() {
        variables.clear();
        log.info("🗑️ 清空所有变量");
    }
    
    /**
     * 获取所有变量
     * 
     * @return 变量映射的副本
     */
    public Map<String, Integer> getAllVariables() {
        return new HashMap<>(variables);
    }
    
    /**
     * 显示当前上下文状态
     */
    public void showStatus() {
        log.info("📊 当前上下文状态:");
        if (variables.isEmpty()) {
            log.info("   无变量定义");
        } else {
            variables.forEach((name, value) -> 
                log.info("   {} = {}", name, value));
        }
    }
}
