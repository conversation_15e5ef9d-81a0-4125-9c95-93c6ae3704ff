package com.learning.designpatterns.behavioral.iterator;

/**
 * 聚合接口
 * 
 * 定义创建迭代器的接口
 * 
 * @param <T> 元素类型
 * <AUTHOR>
 */
public interface Aggregate<T> {
    
    /**
     * 创建迭代器
     * 
     * @return 迭代器实例
     */
    Iterator<T> createIterator();
    
    /**
     * 获取元素数量
     * 
     * @return 元素数量
     */
    int size();
    
    /**
     * 检查是否为空
     * 
     * @return 如果为空返回true，否则返回false
     */
    boolean isEmpty();
}
