package com.learning.designpatterns.behavioral.mediator;

/**
 * 智能设备抽象类
 * 
 * <AUTHOR>
 */
public abstract class SmartDevice {
    
    protected String deviceName;
    protected SmartHomeMediator mediator;
    protected boolean isOn;
    
    public SmartDevice(String deviceName, SmartHomeMediator mediator) {
        this.deviceName = deviceName;
        this.mediator = mediator;
        this.isOn = false;
    }
    
    /**
     * 开启设备
     */
    public abstract void turnOn();
    
    /**
     * 关闭设备
     */
    public abstract void turnOff();
    
    /**
     * 设置设备状态
     * 
     * @param setting 设置参数
     */
    public abstract void setSetting(String setting);
    
    /**
     * 报告设备状态
     * 
     * @param status 状态信息
     */
    public void reportStatus(String status) {
        mediator.deviceStatusChanged(this, status);
    }
    
    /**
     * 获取设备名称
     * 
     * @return 设备名称
     */
    public String getDeviceName() {
        return deviceName;
    }
    
    /**
     * 获取设备状态
     * 
     * @return 设备状态
     */
    public boolean isOn() {
        return isOn;
    }
}
