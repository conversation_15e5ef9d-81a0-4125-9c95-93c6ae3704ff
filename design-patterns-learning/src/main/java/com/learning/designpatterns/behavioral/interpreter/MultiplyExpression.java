package com.learning.designpatterns.behavioral.interpreter;

import lombok.extern.slf4j.Slf4j;

/**
 * 乘法表达式（非终结符表达式）
 * 
 * 表示两个表达式的乘法运算
 * 
 * <AUTHOR>
 */
@Slf4j
public class MultiplyExpression implements Expression {
    
    private final Expression left;
    private final Expression right;
    
    /**
     * 构造函数
     * 
     * @param left 左操作数表达式
     * @param right 右操作数表达式
     */
    public MultiplyExpression(Expression left, Expression right) {
        this.left = left;
        this.right = right;
        log.info("✖️ 创建乘法表达式: ({}) * ({})", left, right);
    }
    
    @Override
    public int interpret(Context context) {
        log.info("✖️ 开始解释乘法表达式: ({}) * ({})", left, right);
        
        int leftValue = left.interpret(context);
        int rightValue = right.interpret(context);
        int result = leftValue * rightValue;
        
        log.info("✖️ 乘法运算: {} * {} = {}", leftValue, rightValue, result);
        return result;
    }
    
    @Override
    public String toString() {
        return "(" + left + " * " + right + ")";
    }
}
