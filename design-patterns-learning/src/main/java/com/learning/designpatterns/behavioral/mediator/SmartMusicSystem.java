package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能音响系统类
 * 
 * <AUTHOR>
 */
@Slf4j
public class SmartMusicSystem extends SmartDevice {
    
    private int volume = 30;
    
    public SmartMusicSystem(String deviceName, SmartHomeMediator mediator) {
        super(deviceName, mediator);
    }
    
    @Override
    public void turnOn() {
        isOn = true;
        log.info("🎵 {} 已开启 (音量: {}%)", deviceName, volume);
    }
    
    @Override
    public void turnOff() {
        isOn = false;
        log.info("🎵 {} 已关闭", deviceName);
    }
    
    @Override
    public void setSetting(String setting) {
        try {
            volume = Integer.parseInt(setting);
            log.info("🎵 {} 音量调节至 {}%", deviceName, volume);
        } catch (NumberFormatException e) {
            log.info("🎵 {} 设置: {}", deviceName, setting);
        }
    }
}
