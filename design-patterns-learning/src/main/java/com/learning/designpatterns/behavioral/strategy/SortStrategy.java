package com.learning.designpatterns.behavioral.strategy;

/**
 * 排序策略接口
 * 
 * 定义了排序算法的统一接口
 * 
 * <AUTHOR>
 */
public interface SortStrategy {
    
    /**
     * 排序方法
     * 
     * @param data 待排序的数据
     */
    void sort(int[] data);
    
    /**
     * 获取算法名称
     * 
     * @return 算法名称
     */
    String getAlgorithmName();
    
    /**
     * 获取时间复杂度
     * 
     * @return 时间复杂度描述
     */
    String getTimeComplexity();
}
