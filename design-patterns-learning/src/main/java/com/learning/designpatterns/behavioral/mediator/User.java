package com.learning.designpatterns.behavioral.mediator;

/**
 * 用户抽象类
 * 
 * 定义了用户的基本操作
 * 
 * <AUTHOR>
 */
public abstract class User {
    
    protected String name;
    protected ChatRoomMediator mediator;
    
    public User(String name, ChatRoomMediator mediator) {
        this.name = name;
        this.mediator = mediator;
    }
    
    /**
     * 发送群聊消息
     * 
     * @param message 消息内容
     */
    public abstract void sendMessage(String message);
    
    /**
     * 发送私聊消息
     * 
     * @param receiverName 接收者名称
     * @param message 消息内容
     */
    public abstract void sendPrivateMessage(String receiverName, String message);
    
    /**
     * 接收消息
     * 
     * @param message 消息内容
     * @param senderName 发送者名称
     */
    public abstract void receiveMessage(String message, String senderName);
    
    /**
     * 接收私聊消息
     * 
     * @param message 消息内容
     * @param senderName 发送者名称
     */
    public abstract void receivePrivateMessage(String message, String senderName);
    
    /**
     * 获取用户名
     * 
     * @return 用户名
     */
    public String getName() {
        return name;
    }
}
