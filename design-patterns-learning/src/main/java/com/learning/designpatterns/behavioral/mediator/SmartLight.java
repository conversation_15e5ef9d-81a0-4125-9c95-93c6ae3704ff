package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能灯具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class SmartLight extends SmartDevice {
    
    private int brightness = 50;
    
    public SmartLight(String deviceName, SmartHomeMediator mediator) {
        super(deviceName, mediator);
    }
    
    @Override
    public void turnOn() {
        isOn = true;
        log.info("💡 {} 已开启 (亮度: {}%)", deviceName, brightness);
    }
    
    @Override
    public void turnOff() {
        isOn = false;
        log.info("💡 {} 已关闭", deviceName);
    }
    
    @Override
    public void setSetting(String setting) {
        try {
            brightness = Integer.parseInt(setting);
            log.info("💡 {} 亮度调节至 {}%", deviceName, brightness);
        } catch (NumberFormatException e) {
            log.info("💡 {} 设置: {}", deviceName, setting);
        }
    }
}
