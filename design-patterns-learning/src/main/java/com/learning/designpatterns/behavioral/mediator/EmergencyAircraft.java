package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 紧急救援飞机类
 * 
 * <AUTHOR>
 */
@Slf4j
public class EmergencyAircraft extends Aircraft {
    
    public EmergencyAircraft(String flightNumber, String airline, AirTrafficControlMediator mediator) {
        super(flightNumber, airline, mediator);
    }
    
    @Override
    public void requestTakeoff() {
        log.info("🚁 急救机 {} 请求紧急起飞", getIdentifier());
        mediator.requestTakeoff(this);
    }
    
    @Override
    public void requestLanding() {
        log.info("🚁 急救机 {} 请求紧急降落", getIdentifier());
        mediator.requestEmergencyLanding(this);
    }
    
    @Override
    public void receiveInstruction(String instruction) {
        log.info("📻 急救机 {} 收到塔台指令: {}", getIdentifier(), instruction);
    }
}
