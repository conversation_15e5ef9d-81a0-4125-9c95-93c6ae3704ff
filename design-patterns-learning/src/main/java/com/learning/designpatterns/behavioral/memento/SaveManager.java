package com.learning.designpatterns.behavioral.memento;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存档管理器（管理者）
 * 
 * 管理游戏存档的保存、加载和删除
 * 
 * <AUTHOR>
 */
@Slf4j
public class SaveManager {
    
    /**
     * 存档列表
     */
    private final List<GameSave> saves;
    
    /**
     * 快速存档槽位（按槽位号存储）
     */
    private final Map<Integer, GameSave> quickSaves;
    
    /**
     * 最大存档数量
     */
    private final int maxSaves;
    
    /**
     * 构造函数
     * 
     * @param maxSaves 最大存档数量
     */
    public SaveManager(int maxSaves) {
        this.maxSaves = maxSaves;
        this.saves = new ArrayList<>();
        this.quickSaves = new HashMap<>();
        log.info("🗃️ 创建存档管理器 (最大存档数: {})", maxSaves);
    }
    
    /**
     * 默认构造函数（最大存档数为10）
     */
    public SaveManager() {
        this(10);
    }
    
    /**
     * 保存游戏存档
     * 
     * @param save 游戏存档
     * @return 存档索引
     */
    public int saveGame(GameSave save) {
        // 如果达到最大存档数，删除最旧的存档
        if (saves.size() >= maxSaves) {
            GameSave oldestSave = saves.remove(0);
            log.info("🗑️ 删除最旧的存档: {}", oldestSave.getDescription());
        }
        
        saves.add(save);
        int index = saves.size() - 1;
        
        log.info("💾 保存游戏存档[{}]: {}", index, save.getDescription());
        return index;
    }
    
    /**
     * 快速保存到指定槽位
     * 
     * @param slotNumber 槽位号（1-9）
     * @param save 游戏存档
     */
    public void quickSave(int slotNumber, GameSave save) {
        if (slotNumber < 1 || slotNumber > 9) {
            log.error("❌ 无效的快速存档槽位: {}", slotNumber);
            return;
        }
        
        quickSaves.put(slotNumber, save);
        log.info("⚡ 快速保存到槽位{}: {}", slotNumber, save.getDescription());
    }
    
    /**
     * 加载指定索引的存档
     * 
     * @param index 存档索引
     * @return 游戏存档，如果索引无效则返回null
     */
    public GameSave loadGame(int index) {
        if (index < 0 || index >= saves.size()) {
            log.error("❌ 无效的存档索引: {}", index);
            return null;
        }
        
        GameSave save = saves.get(index);
        log.info("📂 加载游戏存档[{}]: {}", index, save.getDescription());
        return save;
    }
    
    /**
     * 快速加载指定槽位的存档
     * 
     * @param slotNumber 槽位号
     * @return 游戏存档，如果槽位为空则返回null
     */
    public GameSave quickLoad(int slotNumber) {
        if (slotNumber < 1 || slotNumber > 9) {
            log.error("❌ 无效的快速存档槽位: {}", slotNumber);
            return null;
        }
        
        GameSave save = quickSaves.get(slotNumber);
        if (save != null) {
            log.info("⚡ 快速加载槽位{}: {}", slotNumber, save.getDescription());
        } else {
            log.info("⚠️ 快速存档槽位{}为空", slotNumber);
        }
        return save;
    }
    
    /**
     * 删除指定索引的存档
     * 
     * @param index 存档索引
     * @return 是否删除成功
     */
    public boolean deleteSave(int index) {
        if (index < 0 || index >= saves.size()) {
            log.error("❌ 无效的存档索引: {}", index);
            return false;
        }
        
        GameSave removedSave = saves.remove(index);
        log.info("🗑️ 删除存档[{}]: {}", index, removedSave.getDescription());
        return true;
    }
    
    /**
     * 删除快速存档槽位
     * 
     * @param slotNumber 槽位号
     * @return 是否删除成功
     */
    public boolean deleteQuickSave(int slotNumber) {
        if (slotNumber < 1 || slotNumber > 9) {
            log.error("❌ 无效的快速存档槽位: {}", slotNumber);
            return false;
        }
        
        GameSave removedSave = quickSaves.remove(slotNumber);
        if (removedSave != null) {
            log.info("🗑️ 删除快速存档槽位{}: {}", slotNumber, removedSave.getDescription());
            return true;
        } else {
            log.info("⚠️ 快速存档槽位{}为空", slotNumber);
            return false;
        }
    }
    
    /**
     * 获取存档数量
     * 
     * @return 存档数量
     */
    public int getSaveCount() {
        return saves.size();
    }
    
    /**
     * 检查是否有存档
     * 
     * @return 是否有存档
     */
    public boolean hasSaves() {
        return !saves.isEmpty();
    }
    
    /**
     * 清空所有存档
     */
    public void clearAllSaves() {
        saves.clear();
        quickSaves.clear();
        log.info("🗑️ 清空所有存档");
    }
    
    /**
     * 显示所有存档
     */
    public void showAllSaves() {
        log.info("🗃️ 存档列表:");
        
        if (saves.isEmpty()) {
            log.info("   无普通存档");
        } else {
            log.info("   普通存档:");
            for (int i = 0; i < saves.size(); i++) {
                log.info("     {}. {}", i, saves.get(i));
            }
        }
        
        log.info("   快速存档:");
        boolean hasQuickSaves = false;
        for (int i = 1; i <= 9; i++) {
            GameSave save = quickSaves.get(i);
            if (save != null) {
                log.info("     槽位{}: {}", i, save);
                hasQuickSaves = true;
            }
        }
        
        if (!hasQuickSaves) {
            log.info("     无快速存档");
        }
        
        log.info("   总计: {} 个普通存档, {} 个快速存档", 
            saves.size(), quickSaves.size());
    }
    
    /**
     * 获取最新的存档
     * 
     * @return 最新的存档，如果没有存档则返回null
     */
    public GameSave getLatestSave() {
        if (saves.isEmpty()) {
            return null;
        }
        return saves.get(saves.size() - 1);
    }
}
