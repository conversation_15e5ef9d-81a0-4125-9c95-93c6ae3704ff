package com.learning.designpatterns.behavioral.interpreter;

import lombok.extern.slf4j.Slf4j;

/**
 * 变量表达式（终结符表达式）
 * 
 * 表示一个变量，其值从上下文中获取
 * 
 * <AUTHOR>
 */
@Slf4j
public class VariableExpression implements Expression {
    
    private final String name;
    
    /**
     * 构造函数
     * 
     * @param name 变量名
     */
    public VariableExpression(String name) {
        this.name = name;
        log.info("📝 创建变量表达式: {}", name);
    }
    
    @Override
    public int interpret(Context context) {
        log.info("📖 解释变量: {}", name);
        return context.getVariable(name);
    }
    
    @Override
    public String toString() {
        return name;
    }
    
    /**
     * 获取变量名
     * 
     * @return 变量名
     */
    public String getName() {
        return name;
    }
}
