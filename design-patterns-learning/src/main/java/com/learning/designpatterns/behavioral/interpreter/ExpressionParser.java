package com.learning.designpatterns.behavioral.interpreter;

import lombok.extern.slf4j.Slf4j;

import java.util.Stack;

/**
 * 表达式解析器
 * 
 * 将字符串表达式解析为表达式对象树
 * 支持基本的四则运算和变量
 * 
 * <AUTHOR>
 */
@Slf4j
public class ExpressionParser {
    
    /**
     * 解析表达式字符串
     * 
     * 支持格式: "a + b * c - d / e"
     * 变量用字母表示，数字直接写
     * 
     * @param expressionString 表达式字符串
     * @return 表达式对象
     */
    public static Expression parse(String expressionString) {
        log.info("🔍 开始解析表达式: {}", expressionString);
        
        // 简化版解析器，支持基本的四则运算
        // 这里实现一个简单的递归下降解析器
        String[] tokens = tokenize(expressionString);
        
        if (tokens.length == 0) {
            throw new IllegalArgumentException("表达式不能为空");
        }
        
        Expression result = parseExpression(tokens, 0, tokens.length - 1);
        log.info("✅ 表达式解析完成: {}", result);
        return result;
    }
    
    /**
     * 将表达式字符串分词
     */
    private static String[] tokenize(String expression) {
        // 移除空格并分割
        expression = expression.replaceAll("\\s+", "");
        
        // 简单的分词，支持数字、变量和运算符
        return expression.split("(?<=\\d)(?=[a-zA-Z+\\-*/])|(?<=[a-zA-Z])(?=[+\\-*/])|(?<=[+\\-*/])(?=[a-zA-Z\\d])");
    }
    
    /**
     * 解析表达式（递归下降）
     */
    private static Expression parseExpression(String[] tokens, int start, int end) {
        if (start > end) {
            throw new IllegalArgumentException("无效的表达式");
        }
        
        if (start == end) {
            // 单个token
            String token = tokens[start];
            if (isNumber(token)) {
                return new NumberExpression(Integer.parseInt(token));
            } else if (isVariable(token)) {
                return new VariableExpression(token);
            } else {
                throw new IllegalArgumentException("无效的token: " + token);
            }
        }
        
        // 查找最低优先级的运算符
        int operatorIndex = findLowestPriorityOperator(tokens, start, end);
        
        if (operatorIndex == -1) {
            throw new IllegalArgumentException("找不到运算符");
        }
        
        String operator = tokens[operatorIndex];
        Expression left = parseExpression(tokens, start, operatorIndex - 1);
        Expression right = parseExpression(tokens, operatorIndex + 1, end);
        
        return switch (operator) {
            case "+" -> new AddExpression(left, right);
            case "-" -> new SubtractExpression(left, right);
            case "*" -> new MultiplyExpression(left, right);
            case "/" -> new DivideExpression(left, right);
            default -> throw new IllegalArgumentException("不支持的运算符: " + operator);
        };
    }
    
    /**
     * 查找最低优先级的运算符
     */
    private static int findLowestPriorityOperator(String[] tokens, int start, int end) {
        // 从右到左查找，优先级：+ - < * /
        for (int i = end; i >= start; i--) {
            if (tokens[i].equals("+") || tokens[i].equals("-")) {
                return i;
            }
        }
        
        for (int i = end; i >= start; i--) {
            if (tokens[i].equals("*") || tokens[i].equals("/")) {
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * 判断是否为数字
     */
    private static boolean isNumber(String token) {
        try {
            Integer.parseInt(token);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 判断是否为变量
     */
    private static boolean isVariable(String token) {
        return token.matches("[a-zA-Z]+");
    }
}
