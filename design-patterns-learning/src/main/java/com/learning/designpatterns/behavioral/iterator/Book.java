package com.learning.designpatterns.behavioral.iterator;

import lombok.Data;
import lombok.AllArgsConstructor;

/**
 * 书籍类
 * 
 * 表示图书馆中的一本书
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class Book {
    
    /**
     * 书名
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * ISBN号
     */
    private String isbn;
    
    /**
     * 出版年份
     */
    private int publishYear;
    
    /**
     * 分类
     */
    private String category;
    
    /**
     * 是否可借阅
     */
    private boolean available;
    
    @Override
    public String toString() {
        return String.format("《%s》- %s (%d年) [%s] %s", 
            title, author, publishYear, category, 
            available ? "可借阅" : "已借出");
    }
    
    /**
     * 简化构造函数
     */
    public Book(String title, String author, String category) {
        this(title, author, "", 2023, category, true);
    }
}
