package com.learning.designpatterns.behavioral.interpreter;

import lombok.extern.slf4j.Slf4j;

/**
 * 解释器模式演示
 * 
 * 演示如何使用解释器模式实现一个简单的数学表达式解析器
 * 
 * 解释器模式的应用场景：
 * 1. 编程语言解释器
 * 2. 数学表达式计算器
 * 3. SQL查询解析器
 * 4. 正则表达式引擎
 * 5. 配置文件解析器
 * 
 * <AUTHOR>
 */
@Slf4j
public class InterpreterDemo {
    
    public static void main(String[] args) {
        log.info("🎯 解释器模式演示开始");
        log.info("=" .repeat(50));
        
        // 演示1: 基本数学表达式解析
        demonstrateBasicExpressions();
        
        log.info("");
        
        // 演示2: 变量表达式解析
        demonstrateVariableExpressions();
        
        log.info("");
        
        // 演示3: 复杂表达式解析
        demonstrateComplexExpressions();
        
        log.info("");
        
        // 演示4: 表达式解析器
        demonstrateExpressionParser();
        
        log.info("=" .repeat(50));
        log.info("✅ 解释器模式演示完成");
    }
    
    /**
     * 演示基本数学表达式
     */
    private static void demonstrateBasicExpressions() {
        log.info("📊 演示1: 基本数学表达式");
        log.info("-" .repeat(30));
        
        Context context = new Context();
        
        // 创建表达式: 10 + 5
        Expression expr1 = new AddExpression(
            new NumberExpression(10),
            new NumberExpression(5)
        );
        
        log.info("表达式: {}", expr1);
        int result1 = expr1.interpret(context);
        log.info("结果: {}", result1);
        
        log.info("");
        
        // 创建表达式: 20 - 8
        Expression expr2 = new SubtractExpression(
            new NumberExpression(20),
            new NumberExpression(8)
        );
        
        log.info("表达式: {}", expr2);
        int result2 = expr2.interpret(context);
        log.info("结果: {}", result2);
        
        log.info("");
        
        // 创建表达式: 6 * 7
        Expression expr3 = new MultiplyExpression(
            new NumberExpression(6),
            new NumberExpression(7)
        );
        
        log.info("表达式: {}", expr3);
        int result3 = expr3.interpret(context);
        log.info("结果: {}", result3);
        
        log.info("");
        
        // 创建表达式: 24 / 4
        Expression expr4 = new DivideExpression(
            new NumberExpression(24),
            new NumberExpression(4)
        );
        
        log.info("表达式: {}", expr4);
        int result4 = expr4.interpret(context);
        log.info("结果: {}", result4);
    }
    
    /**
     * 演示变量表达式
     */
    private static void demonstrateVariableExpressions() {
        log.info("📊 演示2: 变量表达式");
        log.info("-" .repeat(30));
        
        Context context = new Context();
        
        // 设置变量
        context.setVariable("x", 15);
        context.setVariable("y", 25);
        context.showStatus();
        
        log.info("");
        
        // 创建表达式: x + y
        Expression expr1 = new AddExpression(
            new VariableExpression("x"),
            new VariableExpression("y")
        );
        
        log.info("表达式: {}", expr1);
        int result1 = expr1.interpret(context);
        log.info("结果: {}", result1);
        
        log.info("");
        
        // 创建表达式: y - x
        Expression expr2 = new SubtractExpression(
            new VariableExpression("y"),
            new VariableExpression("x")
        );
        
        log.info("表达式: {}", expr2);
        int result2 = expr2.interpret(context);
        log.info("结果: {}", result2);
        
        log.info("");
        
        // 修改变量值
        context.setVariable("x", 30);
        log.info("修改变量x的值后:");
        
        log.info("表达式: {}", expr2);
        int result3 = expr2.interpret(context);
        log.info("结果: {}", result3);
    }
    
    /**
     * 演示复杂表达式
     */
    private static void demonstrateComplexExpressions() {
        log.info("📊 演示3: 复杂表达式");
        log.info("-" .repeat(30));
        
        Context context = new Context();
        context.setVariable("a", 10);
        context.setVariable("b", 5);
        context.setVariable("c", 3);
        context.showStatus();
        
        log.info("");
        
        // 创建复杂表达式: (a + b) * c
        Expression expr1 = new MultiplyExpression(
            new AddExpression(
                new VariableExpression("a"),
                new VariableExpression("b")
            ),
            new VariableExpression("c")
        );
        
        log.info("表达式: {}", expr1);
        int result1 = expr1.interpret(context);
        log.info("结果: {}", result1);
        
        log.info("");
        
        // 创建复杂表达式: a * b + c
        Expression expr2 = new AddExpression(
            new MultiplyExpression(
                new VariableExpression("a"),
                new VariableExpression("b")
            ),
            new VariableExpression("c")
        );
        
        log.info("表达式: {}", expr2);
        int result2 = expr2.interpret(context);
        log.info("结果: {}", result2);
        
        log.info("");
        
        // 创建复杂表达式: (a - b) / (c - 1)
        Expression expr3 = new DivideExpression(
            new SubtractExpression(
                new VariableExpression("a"),
                new VariableExpression("b")
            ),
            new SubtractExpression(
                new VariableExpression("c"),
                new NumberExpression(1)
            )
        );
        
        log.info("表达式: {}", expr3);
        int result3 = expr3.interpret(context);
        log.info("结果: {}", result3);
    }
    
    /**
     * 演示表达式解析器
     */
    private static void demonstrateExpressionParser() {
        log.info("📊 演示4: 表达式解析器");
        log.info("-" .repeat(30));
        
        Context context = new Context();
        context.setVariable("x", 8);
        context.setVariable("y", 4);
        context.setVariable("z", 2);
        context.showStatus();
        
        log.info("");
        
        // 解析简单表达式
        try {
            String[] expressions = {
                "x + y",
                "x - y", 
                "x * y",
                "x / y",
                "x + y * z",
                "x * y - z"
            };
            
            for (String exprStr : expressions) {
                log.info("解析表达式: {}", exprStr);
                Expression expr = ExpressionParser.parse(exprStr);
                int result = expr.interpret(context);
                log.info("结果: {} = {}", expr, result);
                log.info("");
            }
            
        } catch (Exception e) {
            log.error("解析表达式时发生错误: {}", e.getMessage());
        }
    }
}
