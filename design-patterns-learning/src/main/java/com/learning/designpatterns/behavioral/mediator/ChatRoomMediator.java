package com.learning.designpatterns.behavioral.mediator;

/**
 * 聊天室中介者接口
 * 
 * 定义了聊天室中介者的基本操作
 * 
 * <AUTHOR>
 */
public interface ChatRoomMediator {
    
    /**
     * 添加用户到聊天室
     * 
     * @param user 用户
     */
    void addUser(User user);
    
    /**
     * 从聊天室移除用户
     * 
     * @param user 用户
     */
    void removeUser(User user);
    
    /**
     * 发送群聊消息
     * 
     * @param message 消息内容
     * @param sender 发送者
     */
    void sendMessage(String message, User sender);
    
    /**
     * 发送私聊消息
     * 
     * @param message 消息内容
     * @param sender 发送者
     * @param receiverName 接收者名称
     */
    void sendPrivateMessage(String message, User sender, String receiverName);
    
    /**
     * 显示在线用户
     */
    void showOnlineUsers();
    
    /**
     * 获取聊天室名称
     * 
     * @return 聊天室名称
     */
    String getRoomName();
}
