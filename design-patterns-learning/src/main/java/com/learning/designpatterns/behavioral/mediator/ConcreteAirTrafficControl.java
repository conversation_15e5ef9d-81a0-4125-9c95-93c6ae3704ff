package com.learning.designpatterns.behavioral.mediator;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 具体航空交通管制中介者
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class ConcreteAirTrafficControl implements AirTrafficControlMediator {
    
    private final String towerName;
    private final List<Aircraft> aircraftList;
    private String currentWeather;
    private String visibility;
    private boolean runwayAvailable;
    
    public ConcreteAirTrafficControl(String towerName) {
        this.towerName = towerName;
        this.aircraftList = new ArrayList<>();
        this.currentWeather = "晴朗";
        this.visibility = "良好";
        this.runwayAvailable = true;
    }
    
    @Override
    public void requestTakeoff(Aircraft aircraft) {
        if (!aircraftList.contains(aircraft)) {
            aircraftList.add(aircraft);
        }
        
        if (runwayAvailable && "良好".equals(visibility)) {
            runwayAvailable = false;
            aircraft.receiveInstruction("起飞许可已批准，跑道清空，注意安全");
            log.info("🏢 塔台: {} 起飞许可已批准", aircraft.getIdentifier());
            
            // 模拟起飞完成
            new Thread(() -> {
                try {
                    Thread.sleep(2000);
                    runwayAvailable = true;
                    log.info("🏢 塔台: 跑道已清空，可接受下一个请求");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        } else {
            aircraft.receiveInstruction("起飞请求被拒绝，跑道繁忙或天气条件不佳，请等待");
            log.info("🏢 塔台: {} 起飞请求被拒绝", aircraft.getIdentifier());
        }
    }
    
    @Override
    public void requestLanding(Aircraft aircraft) {
        if (!aircraftList.contains(aircraft)) {
            aircraftList.add(aircraft);
        }
        
        if (runwayAvailable && "良好".equals(visibility)) {
            runwayAvailable = false;
            aircraft.receiveInstruction("降落许可已批准，跑道准备就绪");
            log.info("🏢 塔台: {} 降落许可已批准", aircraft.getIdentifier());
            
            // 模拟降落完成
            new Thread(() -> {
                try {
                    Thread.sleep(2000);
                    runwayAvailable = true;
                    log.info("🏢 塔台: 跑道已清空，可接受下一个请求");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        } else {
            aircraft.receiveInstruction("降落请求被拒绝，跑道繁忙或天气条件不佳，请盘旋等待");
            log.info("🏢 塔台: {} 降落请求被拒绝", aircraft.getIdentifier());
        }
    }
    
    @Override
    public void requestEmergencyLanding(Aircraft aircraft) {
        if (!aircraftList.contains(aircraft)) {
            aircraftList.add(aircraft);
        }
        
        // 紧急情况优先处理
        runwayAvailable = true; // 强制清空跑道
        aircraft.receiveInstruction("紧急降落许可已批准，跑道已清空，所有其他飞机请避让");
        log.info("🚨 塔台: {} 紧急降落许可已批准，跑道已清空", aircraft.getIdentifier());
        
        // 通知其他飞机
        for (Aircraft otherAircraft : aircraftList) {
            if (!otherAircraft.equals(aircraft)) {
                otherAircraft.receiveInstruction("紧急情况，请避让并等待进一步指令");
            }
        }
    }
    
    @Override
    public void updateWeatherCondition(String weather, String visibility) {
        this.currentWeather = weather;
        this.visibility = visibility;
        log.info("🌤️ 塔台: 天气更新 - {}, 能见度: {}", weather, visibility);
        
        // 通知所有飞机天气变化
        for (Aircraft aircraft : aircraftList) {
            aircraft.receiveInstruction(String.format("天气更新: %s, 能见度: %s", weather, visibility));
        }
    }
    
    @Override
    public void showAirportStatus() {
        log.info("📊 {} 状态报告:", towerName);
        log.info("   天气: {}", currentWeather);
        log.info("   能见度: {}", visibility);
        log.info("   跑道状态: {}", runwayAvailable ? "可用" : "繁忙");
        log.info("   管制飞机数: {}", aircraftList.size());
        for (Aircraft aircraft : aircraftList) {
            log.info("     • {}", aircraft.getIdentifier());
        }
    }
}
