package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能窗帘类
 * 
 * <AUTHOR>
 */
@Slf4j
public class SmartCurtain extends SmartDevice {
    
    private int openLevel = 0; // 0-100%
    
    public SmartCurtain(String deviceName, SmartHomeMediator mediator) {
        super(deviceN<PERSON>, mediator);
    }
    
    @Override
    public void turnOn() {
        isOn = true;
        openLevel = 100;
        log.info("🪟 {} 已打开 (开启度: {}%)", deviceName, openLevel);
    }
    
    @Override
    public void turnOff() {
        isOn = false;
        openLevel = 0;
        log.info("🪟 {} 已关闭", deviceName);
    }
    
    @Override
    public void setSetting(String setting) {
        try {
            openLevel = Integer.parseInt(setting);
            log.info("🪟 {} 开启度调节至 {}%", deviceName, openLevel);
        } catch (NumberFormatException e) {
            log.info("🪟 {} 设置: {}", deviceName, setting);
        }
    }
}
