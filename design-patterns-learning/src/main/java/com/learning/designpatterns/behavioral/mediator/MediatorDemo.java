package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 中介者模式演示类
 * 
 * 中介者模式定义了一个中介对象来封装一系列对象之间的交互。
 * 中介者使各对象不需要显式地相互引用，从而使其耦合松散，而且可以独立地改变它们之间的交互。
 * 
 * 应用场景：
 * - 聊天室系统（用户之间的消息传递）
 * - 航空交通管制系统（飞机与塔台的通信）
 * - 智能家居控制系统（设备间的协调）
 * - 对话框中的控件交互
 * - 工作流系统（任务间的协调）
 * 
 * <AUTHOR>
 */
@Slf4j
public class MediatorDemo {
    
    public static void main(String[] args) {
        log.info("=== 中介者模式演示 ===");
        
        // 演示聊天室系统
        demonstrateChatRoom();
        
        // 演示航空交通管制系统
        demonstrateAirTrafficControl();
        
        // 演示智能家居系统
        demonstrateSmartHome();
    }
    
    private static void demonstrateChatRoom() {
        log.info("\n--- 聊天室中介者模式演示 ---");
        
        // 创建聊天室中介者
        ChatRoomMediator chatRoom = new ConcreteChatRoom("技术交流群");
        
        // 创建用户
        User alice = new ConcreteUser("Alice", chatRoom);
        User bob = new ConcreteUser("Bob", chatRoom);
        User charlie = new ConcreteUser("Charlie", chatRoom);
        User diana = new ConcreteUser("Diana", chatRoom);
        
        log.info("💬 聊天室 '{}' 已创建", chatRoom.getRoomName());
        
        // 用户加入聊天室
        log.info("\n👥 用户加入聊天室:");
        chatRoom.addUser(alice);
        chatRoom.addUser(bob);
        chatRoom.addUser(charlie);
        chatRoom.addUser(diana);
        
        // 群聊消息
        log.info("\n💬 群聊消息:");
        alice.sendMessage("大家好！我是新来的Alice");
        bob.sendMessage("欢迎Alice！我是Bob");
        charlie.sendMessage("Hello everyone! 我是Charlie");
        
        // 私聊消息
        log.info("\n🔒 私聊消息:");
        alice.sendPrivateMessage("Bob", "Bob，你好！能私下聊聊吗？");
        bob.sendPrivateMessage("Alice", "当然可以！有什么问题吗？");
        
        // 用户离开
        log.info("\n👋 用户离开:");
        chatRoom.removeUser(charlie);
        
        // 继续聊天
        log.info("\n💬 继续群聊:");
        diana.sendMessage("Charlie走了吗？");
        alice.sendMessage("是的，他刚刚离开了");
        
        // 显示在线用户
        log.info("\n📊 聊天室统计:");
        chatRoom.showOnlineUsers();
    }
    
    private static void demonstrateAirTrafficControl() {
        log.info("\n--- 航空交通管制中介者模式演示 ---");
        
        // 创建航空交通管制中介者
        AirTrafficControlMediator atc = new ConcreteAirTrafficControl("北京首都机场塔台");
        
        // 创建飞机
        Aircraft flight001 = new CommercialAircraft("CA001", "北京航空", atc);
        Aircraft flight002 = new CommercialAircraft("MU002", "东方航空", atc);
        Aircraft flight003 = new CargoAircraft("CK003", "货运航空", atc);
        Aircraft emergency = new EmergencyAircraft("EM999", "急救飞机", atc);
        
        log.info("🏢 {} 开始工作", atc.getTowerName());
        
        // 飞机请求起飞和降落
        log.info("\n✈️ 飞机请求起飞:");
        flight001.requestTakeoff();
        flight002.requestTakeoff();
        flight003.requestTakeoff();
        
        log.info("\n🛬 飞机请求降落:");
        flight001.requestLanding();
        flight002.requestLanding();
        
        // 紧急情况
        log.info("\n🚨 紧急情况:");
        emergency.requestEmergencyLanding();
        
        // 天气变化
        log.info("\n🌧️ 天气变化:");
        atc.updateWeatherCondition("大雨", "能见度低");
        
        // 继续请求
        log.info("\n✈️ 天气变化后的请求:");
        flight003.requestTakeoff();
        
        // 显示机场状态
        log.info("\n📊 机场状态:");
        atc.showAirportStatus();
    }
    
    private static void demonstrateSmartHome() {
        log.info("\n--- 智能家居中介者模式演示 ---");
        
        // 创建智能家居中介者
        SmartHomeMediator smartHome = new ConcreteSmartHome("智能家居控制中心");
        
        // 创建智能设备
        SmartDevice light = new SmartLight("客厅灯", smartHome);
        SmartDevice aircon = new SmartAirConditioner("空调", smartHome);
        SmartDevice curtain = new SmartCurtain("窗帘", smartHome);
        SmartDevice security = new SmartSecuritySystem("安防系统", smartHome);
        SmartDevice music = new SmartMusicSystem("音响系统", smartHome);
        
        log.info("🏠 {} 已启动", smartHome.getSystemName());
        
        // 注册设备
        log.info("\n📱 注册智能设备:");
        smartHome.registerDevice(light);
        smartHome.registerDevice(aircon);
        smartHome.registerDevice(curtain);
        smartHome.registerDevice(security);
        smartHome.registerDevice(music);
        
        // 场景模式
        log.info("\n🌅 激活'回家模式':");
        smartHome.activateScene("回家模式");
        
        log.info("\n🌙 激活'睡眠模式':");
        smartHome.activateScene("睡眠模式");
        
        log.info("\n🎬 激活'观影模式':");
        smartHome.activateScene("观影模式");
        
        // 设备状态变化
        log.info("\n🔧 设备状态变化:");
        security.reportStatus("检测到异常活动");
        
        // 显示系统状态
        log.info("\n📊 智能家居状态:");
        smartHome.showSystemStatus();
        
        log.info("\n💡 中介者模式的优势:");
        log.info("   ✅ 减少对象间的直接依赖");
        log.info("   ✅ 集中控制复杂的交互逻辑");
        log.info("   ✅ 提高系统的可维护性");
        log.info("   ✅ 支持松耦合的设计");
    }
}
