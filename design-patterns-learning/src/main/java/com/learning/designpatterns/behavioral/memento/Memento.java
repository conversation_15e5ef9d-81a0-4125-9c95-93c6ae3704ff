package com.learning.designpatterns.behavioral.memento;

import lombok.Getter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 备忘录类
 * 
 * 存储发起人对象的内部状态
 * 备忘录可以根据需要保存发起人的部分或全部状态
 * 
 * <AUTHOR>
 */
@Getter
public class Memento {
    
    /**
     * 保存的状态数据
     */
    private final String state;
    
    /**
     * 创建时间
     */
    private final LocalDateTime timestamp;
    
    /**
     * 备忘录描述
     */
    private final String description;
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 构造函数
     * 
     * @param state 要保存的状态
     * @param description 备忘录描述
     */
    public Memento(String state, String description) {
        this.state = state;
        this.description = description;
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 构造函数（无描述）
     * 
     * @param state 要保存的状态
     */
    public Memento(String state) {
        this(state, "自动保存");
    }
    
    /**
     * 获取格式化的时间戳
     * 
     * @return 格式化的时间字符串
     */
    public String getFormattedTimestamp() {
        return timestamp.format(TIME_FORMATTER);
    }
    
    @Override
    public String toString() {
        return String.format("备忘录[%s] - %s (%s)", 
            description, getFormattedTimestamp(), 
            state.length() > 50 ? state.substring(0, 50) + "..." : state);
    }
}
