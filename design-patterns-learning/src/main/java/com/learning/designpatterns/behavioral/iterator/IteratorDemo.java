package com.learning.designpatterns.behavioral.iterator;

import lombok.extern.slf4j.Slf4j;

/**
 * 迭代器模式演示
 * 
 * 演示如何使用迭代器模式遍历不同类型的集合
 * 
 * 迭代器模式的应用场景：
 * 1. 集合框架（ArrayList、LinkedList等）
 * 2. 数据库结果集遍历
 * 3. 文件系统目录遍历
 * 4. 树形结构遍历
 * 5. 图形界面组件遍历
 * 
 * <AUTHOR>
 */
@Slf4j
public class IteratorDemo {
    
    public static void main(String[] args) {
        log.info("🎯 迭代器模式演示开始");
        log.info("=" .repeat(50));
        
        // 演示1: 书架遍历
        demonstrateBookShelfIteration();
        
        log.info("");
        
        // 演示2: 书籍章节遍历
        demonstrateBookContentIteration();
        
        log.info("");
        
        // 演示3: 多种迭代方式
        demonstrateMultipleIterationMethods();
        
        log.info("");
        
        // 演示4: 迭代器重置和位置跟踪
        demonstrateIteratorReset();
        
        log.info("=" .repeat(50));
        log.info("✅ 迭代器模式演示完成");
    }
    
    /**
     * 演示书架遍历
     */
    private static void demonstrateBookShelfIteration() {
        log.info("📚 演示1: 书架遍历");
        log.info("-" .repeat(30));
        
        // 创建书架
        BookShelf programmingShelf = new BookShelf("编程技术书架");
        
        // 添加书籍
        programmingShelf.addBook(new Book("Java核心技术", "Cay S. Horstmann", "编程"));
        programmingShelf.addBook(new Book("设计模式", "Gang of Four", "编程"));
        programmingShelf.addBook(new Book("重构", "Martin Fowler", "编程"));
        programmingShelf.addBook(new Book("代码整洁之道", "Robert C. Martin", "编程"));
        
        log.info("");
        programmingShelf.showShelfInfo();
        
        log.info("");
        log.info("🔄 开始遍历书架:");
        
        // 使用迭代器遍历
        Iterator<Book> iterator = programmingShelf.createIterator();
        int count = 1;
        
        while (iterator.hasNext()) {
            Book book = iterator.next();
            log.info("{}. {}", count++, book);
        }
    }
    
    /**
     * 演示书籍章节遍历
     */
    private static void demonstrateBookContentIteration() {
        log.info("📖 演示2: 书籍章节遍历");
        log.info("-" .repeat(30));
        
        // 创建书籍内容
        BookContent javaBook = new BookContent("Java编程思想");
        
        // 添加章节
        javaBook.addChapter(new Chapter(1, "对象导论", 25));
        javaBook.addChapter(new Chapter(2, "一切都是对象", 30));
        javaBook.addChapter(new Chapter(3, "操作符", 28));
        javaBook.addChapter(new Chapter(4, "控制执行流程", 35));
        javaBook.addChapter(new Chapter(5, "初始化与清理", 40));
        
        log.info("");
        javaBook.showContentInfo();
        
        log.info("");
        log.info("🔄 开始遍历章节:");
        
        // 使用迭代器遍历章节
        Iterator<Chapter> iterator = javaBook.createIterator();
        
        while (iterator.hasNext()) {
            Chapter chapter = iterator.next();
            log.info("{}", chapter);
        }
    }
    
    /**
     * 演示多种迭代方式
     */
    private static void demonstrateMultipleIterationMethods() {
        log.info("🔄 演示3: 多种迭代方式");
        log.info("-" .repeat(30));
        
        // 创建书籍内容
        BookContent designPatternsBook = new BookContent("设计模式");
        
        // 添加章节并设置一些为已读
        Chapter chapter1 = new Chapter(1, "引言", 20);
        Chapter chapter2 = new Chapter(2, "实例研究", 45);
        Chapter chapter3 = new Chapter(3, "创建型模式", 60);
        Chapter chapter4 = new Chapter(4, "结构型模式", 55);
        Chapter chapter5 = new Chapter(5, "行为型模式", 80);
        
        // 标记前两章为已读
        chapter1.markAsRead();
        chapter2.markAsRead();
        
        designPatternsBook.addChapter(chapter1);
        designPatternsBook.addChapter(chapter2);
        designPatternsBook.addChapter(chapter3);
        designPatternsBook.addChapter(chapter4);
        designPatternsBook.addChapter(chapter5);
        
        log.info("");
        designPatternsBook.showContentInfo();
        
        // 1. 正向遍历
        log.info("");
        log.info("📖 正向遍历:");
        Iterator<Chapter> forwardIterator = designPatternsBook.createIterator();
        while (forwardIterator.hasNext()) {
            Chapter chapter = forwardIterator.next();
            log.info("  {}", chapter);
        }
        
        // 2. 反向遍历
        log.info("");
        log.info("📖 反向遍历:");
        Iterator<Chapter> reverseIterator = designPatternsBook.createReverseIterator();
        while (reverseIterator.hasNext()) {
            Chapter chapter = reverseIterator.next();
            log.info("  {}", chapter);
        }
        
        // 3. 只遍历未读章节
        log.info("");
        log.info("📖 未读章节遍历:");
        Iterator<Chapter> unreadIterator = designPatternsBook.createUnreadIterator();
        while (unreadIterator.hasNext()) {
            Chapter chapter = unreadIterator.next();
            log.info("  {}", chapter);
        }
    }
    
    /**
     * 演示迭代器重置和位置跟踪
     */
    private static void demonstrateIteratorReset() {
        log.info("🔄 演示4: 迭代器重置和位置跟踪");
        log.info("-" .repeat(30));
        
        // 创建简单的书架
        BookShelf shelf = new BookShelf("测试书架");
        shelf.addBook(new Book("书籍A", "作者A", "分类A"));
        shelf.addBook(new Book("书籍B", "作者B", "分类B"));
        shelf.addBook(new Book("书籍C", "作者C", "分类C"));
        
        log.info("");
        
        Iterator<Book> iterator = shelf.createIterator();
        
        // 第一次遍历（部分）
        log.info("📖 第一次遍历（只读前两本）:");
        for (int i = 0; i < 2 && iterator.hasNext(); i++) {
            Book book = iterator.next();
            log.info("  位置{}: {}", iterator.getCurrentPosition(), book.getTitle());
        }
        
        log.info("当前位置: {}", iterator.getCurrentPosition());
        log.info("还有下一个: {}", iterator.hasNext());
        
        // 重置迭代器
        log.info("");
        log.info("🔄 重置迭代器");
        iterator.reset();
        log.info("重置后位置: {}", iterator.getCurrentPosition());
        
        // 第二次完整遍历
        log.info("");
        log.info("📖 第二次完整遍历:");
        while (iterator.hasNext()) {
            Book book = iterator.next();
            log.info("  位置{}: {}", iterator.getCurrentPosition(), book.getTitle());
        }
        
        log.info("遍历完成，当前位置: {}", iterator.getCurrentPosition());
    }
}
