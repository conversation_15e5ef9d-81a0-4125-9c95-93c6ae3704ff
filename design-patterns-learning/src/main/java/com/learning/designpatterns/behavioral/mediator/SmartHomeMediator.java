package com.learning.designpatterns.behavioral.mediator;

/**
 * 智能家居中介者接口
 * 
 * <AUTHOR>
 */
public interface SmartHomeMediator {
    
    /**
     * 注册设备
     * 
     * @param device 智能设备
     */
    void registerDevice(SmartDevice device);
    
    /**
     * 激活场景模式
     * 
     * @param sceneName 场景名称
     */
    void activateScene(String sceneName);
    
    /**
     * 设备状态变化通知
     * 
     * @param device 设备
     * @param status 状态
     */
    void deviceStatusChanged(SmartDevice device, String status);
    
    /**
     * 显示系统状态
     */
    void showSystemStatus();
    
    /**
     * 获取系统名称
     * 
     * @return 系统名称
     */
    String getSystemName();
}
