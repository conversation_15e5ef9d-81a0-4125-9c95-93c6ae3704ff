package com.learning.designpatterns.behavioral.iterator;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * 书架类（具体聚合）
 * 
 * 实现了聚合接口，管理书籍集合
 * 
 * <AUTHOR>
 */
@Slf4j
public class BookShelf implements Aggregate<Book> {
    
    private final List<Book> books;
    private final String shelfName;
    
    /**
     * 构造函数
     * 
     * @param shelfName 书架名称
     */
    public BookShelf(String shelfName) {
        this.shelfName = shelfName;
        this.books = new ArrayList<>();
        log.info("📚 创建书架: {}", shelfName);
    }
    
    /**
     * 添加书籍
     * 
     * @param book 书籍
     */
    public void addBook(Book book) {
        books.add(book);
        log.info("📖 添加书籍到{}: {}", shelfName, book.getTitle());
    }
    
    /**
     * 移除书籍
     * 
     * @param book 书籍
     * @return 是否移除成功
     */
    public boolean removeBook(Book book) {
        boolean removed = books.remove(book);
        if (removed) {
            log.info("🗑️ 从{}移除书籍: {}", shelfName, book.getTitle());
        }
        return removed;
    }
    
    /**
     * 根据索引获取书籍
     * 
     * @param index 索引
     * @return 书籍
     */
    public Book getBook(int index) {
        if (index < 0 || index >= books.size()) {
            throw new IndexOutOfBoundsException("索引超出范围: " + index);
        }
        return books.get(index);
    }
    
    @Override
    public Iterator<Book> createIterator() {
        log.info("🔄 为{}创建迭代器", shelfName);
        return new BookShelfIterator();
    }
    
    @Override
    public int size() {
        return books.size();
    }
    
    @Override
    public boolean isEmpty() {
        return books.isEmpty();
    }
    
    /**
     * 获取书架名称
     * 
     * @return 书架名称
     */
    public String getShelfName() {
        return shelfName;
    }
    
    /**
     * 显示书架信息
     */
    public void showShelfInfo() {
        log.info("📚 书架信息: {}", shelfName);
        log.info("   书籍总数: {}", books.size());
        if (!books.isEmpty()) {
            log.info("   书籍列表:");
            for (int i = 0; i < books.size(); i++) {
                log.info("     {}. {}", i + 1, books.get(i));
            }
        }
    }
    
    /**
     * 书架迭代器（具体迭代器）
     */
    private class BookShelfIterator implements Iterator<Book> {
        
        private int currentIndex = 0;
        
        @Override
        public boolean hasNext() {
            return currentIndex < books.size();
        }
        
        @Override
        public Book next() {
            if (!hasNext()) {
                throw new NoSuchElementException("没有更多书籍");
            }
            
            Book book = books.get(currentIndex);
            currentIndex++;
            
            log.info("📖 迭代器返回书籍[{}]: {}", currentIndex, book.getTitle());
            return book;
        }
        
        @Override
        public void reset() {
            currentIndex = 0;
            log.info("🔄 迭代器重置到起始位置");
        }
        
        @Override
        public int getCurrentPosition() {
            return currentIndex;
        }
    }
}
