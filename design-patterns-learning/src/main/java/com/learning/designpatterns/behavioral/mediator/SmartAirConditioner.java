package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能空调类
 * 
 * <AUTHOR>
 */
@Slf4j
public class SmartAirConditioner extends SmartDevice {
    
    private int temperature = 24;
    
    public SmartAirConditioner(String deviceName, SmartHomeMediator mediator) {
        super(deviceName, mediator);
    }
    
    @Override
    public void turnOn() {
        isOn = true;
        log.info("❄️ {} 已开启 (温度: {}°C)", deviceName, temperature);
    }
    
    @Override
    public void turnOff() {
        isOn = false;
        log.info("❄️ {} 已关闭", deviceName);
    }
    
    @Override
    public void setSetting(String setting) {
        try {
            temperature = Integer.parseInt(setting);
            log.info("❄️ {} 温度调节至 {}°C", deviceName, temperature);
        } catch (NumberFormatException e) {
            log.info("❄️ {} 设置: {}", deviceName, setting);
        }
    }
}
