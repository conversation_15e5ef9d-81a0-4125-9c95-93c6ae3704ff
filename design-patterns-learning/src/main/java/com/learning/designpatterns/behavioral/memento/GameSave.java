package com.learning.designpatterns.behavioral.memento;

import lombok.Getter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 游戏存档类（备忘录）
 * 
 * 保存游戏状态的快照
 * 
 * <AUTHOR>
 */
@Getter
public class GameSave {
    
    /**
     * 玩家等级
     */
    private final int level;
    
    /**
     * 玩家经验值
     */
    private final int experience;
    
    /**
     * 玩家生命值
     */
    private final int health;
    
    /**
     * 玩家魔法值
     */
    private final int mana;
    
    /**
     * 当前关卡
     */
    private final int currentStage;
    
    /**
     * 金币数量
     */
    private final int gold;
    
    /**
     * 玩家名称
     */
    private final String playerName;
    
    /**
     * 存档描述
     */
    private final String description;
    
    /**
     * 存档时间
     */
    private final LocalDateTime saveTime;
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 构造函数
     */
    public GameSave(int level, int experience, int health, int mana, 
                   int currentStage, int gold, String playerName, String description) {
        this.level = level;
        this.experience = experience;
        this.health = health;
        this.mana = mana;
        this.currentStage = currentStage;
        this.gold = gold;
        this.playerName = playerName;
        this.description = description;
        this.saveTime = LocalDateTime.now();
    }
    
    /**
     * 获取格式化的存档时间
     * 
     * @return 格式化的时间字符串
     */
    public String getFormattedSaveTime() {
        return saveTime.format(TIME_FORMATTER);
    }
    
    /**
     * 获取存档摘要
     * 
     * @return 存档摘要信息
     */
    public String getSummary() {
        return String.format("等级%d 第%d关 生命%d 金币%d", 
            level, currentStage, health, gold);
    }
    
    @Override
    public String toString() {
        return String.format("存档[%s] - %s (%s)", 
            description, getSummary(), getFormattedSaveTime());
    }
}
