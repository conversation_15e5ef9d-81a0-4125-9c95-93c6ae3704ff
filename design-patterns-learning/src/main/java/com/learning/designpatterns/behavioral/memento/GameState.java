package com.learning.designpatterns.behavioral.memento;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 游戏状态类（发起人）
 * 
 * 表示游戏的当前状态，可以创建和恢复存档
 * 
 * <AUTHOR>
 */
@Data
@Slf4j
public class GameState {
    
    /**
     * 玩家等级
     */
    private int level;
    
    /**
     * 玩家经验值
     */
    private int experience;
    
    /**
     * 玩家生命值
     */
    private int health;
    
    /**
     * 玩家魔法值
     */
    private int mana;
    
    /**
     * 当前关卡
     */
    private int currentStage;
    
    /**
     * 金币数量
     */
    private int gold;
    
    /**
     * 玩家名称
     */
    private String playerName;
    
    /**
     * 构造函数
     * 
     * @param playerName 玩家名称
     */
    public GameState(String playerName) {
        this.playerName = playerName;
        this.level = 1;
        this.experience = 0;
        this.health = 100;
        this.mana = 50;
        this.currentStage = 1;
        this.gold = 100;
        log.info("🎮 创建游戏状态: 玩家 {}", playerName);
    }
    
    /**
     * 升级
     */
    public void levelUp() {
        level++;
        health += 20;
        mana += 10;
        experience = 0;
        log.info("⬆️ 玩家 {} 升级到 {} 级!", playerName, level);
    }
    
    /**
     * 获得经验
     * 
     * @param exp 经验值
     */
    public void gainExperience(int exp) {
        experience += exp;
        log.info("✨ 玩家 {} 获得 {} 经验值 (总计: {})", playerName, exp, experience);
        
        // 检查是否可以升级
        int expNeeded = level * 100;
        if (experience >= expNeeded) {
            levelUp();
        }
    }
    
    /**
     * 受到伤害
     * 
     * @param damage 伤害值
     */
    public void takeDamage(int damage) {
        health = Math.max(0, health - damage);
        log.info("💔 玩家 {} 受到 {} 点伤害 (剩余生命: {})", playerName, damage, health);
        
        if (health == 0) {
            log.info("💀 玩家 {} 死亡!", playerName);
        }
    }
    
    /**
     * 恢复生命
     * 
     * @param healing 恢复量
     */
    public void heal(int healing) {
        int maxHealth = level * 20 + 80;
        health = Math.min(maxHealth, health + healing);
        log.info("💚 玩家 {} 恢复 {} 点生命 (当前生命: {})", playerName, healing, health);
    }
    
    /**
     * 使用魔法
     * 
     * @param cost 魔法消耗
     * @return 是否成功使用
     */
    public boolean useMana(int cost) {
        if (mana >= cost) {
            mana -= cost;
            log.info("🔮 玩家 {} 使用 {} 点魔法 (剩余魔法: {})", playerName, cost, mana);
            return true;
        } else {
            log.info("⚠️ 玩家 {} 魔法不足 (需要: {}, 当前: {})", playerName, cost, mana);
            return false;
        }
    }
    
    /**
     * 进入下一关
     */
    public void nextStage() {
        currentStage++;
        gainExperience(50);
        gold += 100;
        log.info("🚪 玩家 {} 进入第 {} 关", playerName, currentStage);
    }
    
    /**
     * 购买物品
     * 
     * @param cost 物品价格
     * @param itemName 物品名称
     * @return 是否购买成功
     */
    public boolean buyItem(int cost, String itemName) {
        if (gold >= cost) {
            gold -= cost;
            log.info("🛒 玩家 {} 购买了 {} (花费: {} 金币, 剩余: {})", 
                playerName, itemName, cost, gold);
            return true;
        } else {
            log.info("⚠️ 玩家 {} 金币不足购买 {} (需要: {}, 当前: {})", 
                playerName, itemName, cost, gold);
            return false;
        }
    }
    
    /**
     * 创建游戏存档（备忘录）
     * 
     * @param saveDescription 存档描述
     * @return 游戏存档
     */
    public GameSave createSave(String saveDescription) {
        GameSave save = new GameSave(
            level, experience, health, mana, 
            currentStage, gold, playerName, saveDescription
        );
        log.info("💾 创建游戏存档: {}", saveDescription);
        return save;
    }
    
    /**
     * 从存档恢复游戏状态
     * 
     * @param save 游戏存档
     */
    public void loadFromSave(GameSave save) {
        this.level = save.getLevel();
        this.experience = save.getExperience();
        this.health = save.getHealth();
        this.mana = save.getMana();
        this.currentStage = save.getCurrentStage();
        this.gold = save.getGold();
        this.playerName = save.getPlayerName();
        
        log.info("🔄 从存档恢复游戏状态: {}", save.getDescription());
    }
    
    /**
     * 显示游戏状态
     */
    public void showStatus() {
        log.info("🎮 游戏状态 [{}]:", playerName);
        log.info("   等级: {} (经验: {})", level, experience);
        log.info("   生命值: {}", health);
        log.info("   魔法值: {}", mana);
        log.info("   当前关卡: {}", currentStage);
        log.info("   金币: {}", gold);
        
        // 显示状态条
        String healthBar = createStatusBar(health, level * 20 + 80, "❤️");
        String manaBar = createStatusBar(mana, level * 10 + 40, "🔮");
        log.info("   生命条: {}", healthBar);
        log.info("   魔法条: {}", manaBar);
    }
    
    /**
     * 创建状态条
     */
    private String createStatusBar(int current, int max, String icon) {
        int barLength = 10;
        int filled = (int) ((double) current / max * barLength);
        
        StringBuilder bar = new StringBuilder();
        bar.append(icon).append(" ");
        
        for (int i = 0; i < barLength; i++) {
            if (i < filled) {
                bar.append("█");
            } else {
                bar.append("░");
            }
        }
        
        bar.append(String.format(" %d/%d", current, max));
        return bar.toString();
    }
}
