package com.learning.designpatterns.behavioral.mediator;

/**
 * 航空交通管制中介者接口
 * 
 * <AUTHOR>
 */
public interface AirTrafficControlMediator {
    
    /**
     * 处理起飞请求
     * 
     * @param aircraft 飞机
     */
    void requestTakeoff(Aircraft aircraft);
    
    /**
     * 处理降落请求
     * 
     * @param aircraft 飞机
     */
    void requestLanding(Aircraft aircraft);
    
    /**
     * 处理紧急降落请求
     * 
     * @param aircraft 飞机
     */
    void requestEmergencyLanding(Aircraft aircraft);
    
    /**
     * 更新天气条件
     * 
     * @param weather 天气状况
     * @param visibility 能见度
     */
    void updateWeatherCondition(String weather, String visibility);
    
    /**
     * 显示机场状态
     */
    void showAirportStatus();
    
    /**
     * 获取塔台名称
     * 
     * @return 塔台名称
     */
    String getTowerName();
}
