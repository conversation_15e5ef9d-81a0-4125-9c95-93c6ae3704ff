package com.learning.designpatterns.behavioral.memento;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 编辑器历史管理器（管理者）
 * 
 * 负责管理备忘录的存储和检索，但不能访问备忘录的内容
 * 
 * <AUTHOR>
 */
@Slf4j
public class EditorHistory {
    
    /**
     * 备忘录列表
     */
    private final List<Memento> mementos;
    
    /**
     * 当前位置（用于撤销/重做）
     */
    private int currentIndex;
    
    /**
     * 最大历史记录数
     */
    private final int maxHistorySize;
    
    /**
     * 构造函数
     * 
     * @param maxHistorySize 最大历史记录数
     */
    public EditorHistory(int maxHistorySize) {
        this.maxHistorySize = maxHistorySize;
        this.mementos = new ArrayList<>();
        this.currentIndex = -1;
        log.info("📚 创建编辑器历史管理器 (最大记录数: {})", maxHistorySize);
    }
    
    /**
     * 默认构造函数（最大记录数为20）
     */
    public EditorHistory() {
        this(20);
    }
    
    /**
     * 保存备忘录
     * 
     * @param memento 要保存的备忘录
     */
    public void save(Memento memento) {
        // 如果当前不在最新位置，删除后面的历史记录
        if (currentIndex < mementos.size() - 1) {
            mementos.subList(currentIndex + 1, mementos.size()).clear();
            log.info("🗑️ 清除后续历史记录");
        }
        
        // 添加新的备忘录
        mementos.add(memento);
        currentIndex++;
        
        // 如果超过最大记录数，删除最旧的记录
        if (mementos.size() > maxHistorySize) {
            mementos.remove(0);
            currentIndex--;
            log.info("🗑️ 删除最旧的历史记录");
        }
        
        log.info("💾 保存备忘录: {} (历史记录数: {})", 
            memento.getDescription(), mementos.size());
    }
    
    /**
     * 撤销操作
     * 
     * @return 上一个备忘录，如果没有则返回null
     */
    public Memento undo() {
        if (!canUndo()) {
            log.info("⚠️ 无法撤销，已到达历史记录开始");
            return null;
        }
        
        currentIndex--;
        Memento memento = mementos.get(currentIndex);
        log.info("↶ 撤销到: {}", memento.getDescription());
        return memento;
    }
    
    /**
     * 重做操作
     * 
     * @return 下一个备忘录，如果没有则返回null
     */
    public Memento redo() {
        if (!canRedo()) {
            log.info("⚠️ 无法重做，已到达历史记录末尾");
            return null;
        }
        
        currentIndex++;
        Memento memento = mementos.get(currentIndex);
        log.info("↷ 重做到: {}", memento.getDescription());
        return memento;
    }
    
    /**
     * 检查是否可以撤销
     * 
     * @return 是否可以撤销
     */
    public boolean canUndo() {
        return currentIndex > 0;
    }
    
    /**
     * 检查是否可以重做
     * 
     * @return 是否可以重做
     */
    public boolean canRedo() {
        return currentIndex < mementos.size() - 1;
    }
    
    /**
     * 获取当前备忘录
     * 
     * @return 当前备忘录，如果没有则返回null
     */
    public Memento getCurrentMemento() {
        if (currentIndex >= 0 && currentIndex < mementos.size()) {
            return mementos.get(currentIndex);
        }
        return null;
    }
    
    /**
     * 获取历史记录数量
     * 
     * @return 历史记录数量
     */
    public int getHistorySize() {
        return mementos.size();
    }
    
    /**
     * 获取当前位置
     * 
     * @return 当前位置索引
     */
    public int getCurrentIndex() {
        return currentIndex;
    }
    
    /**
     * 清空历史记录
     */
    public void clear() {
        mementos.clear();
        currentIndex = -1;
        log.info("🗑️ 清空所有历史记录");
    }
    
    /**
     * 显示历史记录
     */
    public void showHistory() {
        log.info("📚 历史记录 (当前位置: {}):", currentIndex);
        
        if (mementos.isEmpty()) {
            log.info("   无历史记录");
            return;
        }
        
        for (int i = 0; i < mementos.size(); i++) {
            String marker = (i == currentIndex) ? " -> " : "    ";
            log.info("{}{}. {}", marker, i, mementos.get(i));
        }
        
        log.info("   可撤销: {}, 可重做: {}", canUndo(), canRedo());
    }
    
    /**
     * 跳转到指定的历史记录
     * 
     * @param index 目标索引
     * @return 目标备忘录，如果索引无效则返回null
     */
    public Memento jumpTo(int index) {
        if (index < 0 || index >= mementos.size()) {
            log.info("⚠️ 无效的历史记录索引: {}", index);
            return null;
        }
        
        currentIndex = index;
        Memento memento = mementos.get(currentIndex);
        log.info("🎯 跳转到历史记录[{}]: {}", index, memento.getDescription());
        return memento;
    }
}
