package com.learning.designpatterns.behavioral.memento;

import lombok.extern.slf4j.Slf4j;

/**
 * 文本编辑器类（发起人）
 * 
 * 负责创建备忘录来保存当前状态，以及使用备忘录来恢复状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class TextEditor {
    
    /**
     * 当前文本内容
     */
    private StringBuilder content;
    
    /**
     * 当前光标位置
     */
    private int cursorPosition;
    
    /**
     * 编辑器名称
     */
    private final String editorName;
    
    /**
     * 构造函数
     * 
     * @param editorName 编辑器名称
     */
    public TextEditor(String editorName) {
        this.editorName = editorName;
        this.content = new StringBuilder();
        this.cursorPosition = 0;
        log.info("📝 创建文本编辑器: {}", editorName);
    }
    
    /**
     * 写入文本
     * 
     * @param text 要写入的文本
     */
    public void write(String text) {
        content.insert(cursorPosition, text);
        cursorPosition += text.length();
        log.info("✏️ 写入文本: \"{}\" (光标位置: {})", text, cursorPosition);
    }
    
    /**
     * 删除指定长度的文本
     * 
     * @param length 要删除的字符数
     */
    public void delete(int length) {
        if (length <= 0) return;
        
        int startPos = Math.max(0, cursorPosition - length);
        int endPos = cursorPosition;
        
        if (startPos < content.length()) {
            String deletedText = content.substring(startPos, Math.min(endPos, content.length()));
            content.delete(startPos, Math.min(endPos, content.length()));
            cursorPosition = startPos;
            log.info("🗑️ 删除文本: \"{}\" (光标位置: {})", deletedText, cursorPosition);
        }
    }
    
    /**
     * 移动光标
     * 
     * @param position 新的光标位置
     */
    public void moveCursor(int position) {
        this.cursorPosition = Math.max(0, Math.min(position, content.length()));
        log.info("👆 移动光标到位置: {}", cursorPosition);
    }
    
    /**
     * 替换文本
     * 
     * @param oldText 要替换的文本
     * @param newText 新文本
     */
    public void replace(String oldText, String newText) {
        String contentStr = content.toString();
        int index = contentStr.indexOf(oldText);
        
        if (index != -1) {
            content.replace(index, index + oldText.length(), newText);
            // 调整光标位置
            if (cursorPosition > index) {
                cursorPosition = cursorPosition - oldText.length() + newText.length();
            }
            log.info("🔄 替换文本: \"{}\" -> \"{}\"", oldText, newText);
        } else {
            log.info("⚠️ 未找到要替换的文本: \"{}\"", oldText);
        }
    }
    
    /**
     * 创建备忘录
     * 
     * @param description 备忘录描述
     * @return 备忘录对象
     */
    public Memento createMemento(String description) {
        String state = content.toString() + "|" + cursorPosition;
        Memento memento = new Memento(state, description);
        log.info("💾 创建备忘录: {}", description);
        return memento;
    }
    
    /**
     * 从备忘录恢复状态
     * 
     * @param memento 备忘录对象
     */
    public void restoreFromMemento(Memento memento) {
        String state = memento.getState();
        String[] parts = state.split("\\|");
        
        if (parts.length == 2) {
            this.content = new StringBuilder(parts[0]);
            this.cursorPosition = Integer.parseInt(parts[1]);
            log.info("🔄 从备忘录恢复状态: {} (光标位置: {})", 
                memento.getDescription(), cursorPosition);
        } else {
            log.error("❌ 备忘录格式错误，无法恢复");
        }
    }
    
    /**
     * 获取当前内容
     * 
     * @return 当前文本内容
     */
    public String getContent() {
        return content.toString();
    }
    
    /**
     * 获取当前光标位置
     * 
     * @return 光标位置
     */
    public int getCursorPosition() {
        return cursorPosition;
    }
    
    /**
     * 显示编辑器状态
     */
    public void showStatus() {
        log.info("📊 编辑器状态 [{}]:", editorName);
        log.info("   内容: \"{}\"", content.toString());
        log.info("   光标位置: {}", cursorPosition);
        log.info("   内容长度: {}", content.length());
        
        // 显示光标位置的可视化表示
        if (content.length() > 0) {
            StringBuilder visualization = new StringBuilder(content.toString());
            if (cursorPosition <= visualization.length()) {
                visualization.insert(cursorPosition, "|");
            }
            log.info("   可视化: \"{}\"", visualization.toString());
        }
    }
    
    /**
     * 清空内容
     */
    public void clear() {
        content.setLength(0);
        cursorPosition = 0;
        log.info("🗑️ 清空编辑器内容");
    }
}
