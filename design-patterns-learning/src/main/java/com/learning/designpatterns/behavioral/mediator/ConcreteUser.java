package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 具体用户类
 * 
 * 实现用户的具体行为
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConcreteUser extends User {
    
    public ConcreteUser(String name, ChatRoomMediator mediator) {
        super(name, mediator);
    }
    
    @Override
    public void sendMessage(String message) {
        log.info("👤 {} 发送群聊消息", name);
        mediator.sendMessage(message, this);
    }
    
    @Override
    public void sendPrivateMessage(String receiverName, String message) {
        log.info("👤 {} 发送私聊消息给 {}", name, receiverName);
        mediator.sendPrivateMessage(message, this, receiverName);
    }
    
    @Override
    public void receiveMessage(String message, String senderName) {
        log.info("📨 {} 收到来自 {} 的群聊消息: \"{}\"", name, senderName, message);
    }
    
    @Override
    public void receivePrivateMessage(String message, String senderName) {
        log.info("🔒 {} 收到来自 {} 的私聊消息: \"{}\"", name, senderName, message);
    }
}
