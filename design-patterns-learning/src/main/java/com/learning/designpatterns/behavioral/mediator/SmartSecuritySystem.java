package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 智能安防系统类
 * 
 * <AUTHOR>
 */
@Slf4j
public class SmartSecuritySystem extends SmartDevice {
    
    public SmartSecuritySystem(String deviceName, SmartHomeMediator mediator) {
        super(deviceName, mediator);
    }
    
    @Override
    public void turnOn() {
        isOn = true;
        log.info("🔒 {} 已启动 - 开始监控", deviceName);
    }
    
    @Override
    public void turnOff() {
        isOn = false;
        log.info("🔒 {} 已关闭 - 停止监控", deviceName);
    }
    
    @Override
    public void setSetting(String setting) {
        log.info("🔒 {} 设置: {}", deviceName, setting);
    }
}
