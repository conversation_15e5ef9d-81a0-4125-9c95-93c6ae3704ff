package com.learning.designpatterns.behavioral.mediator;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * 具体智能家居中介者
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
public class ConcreteSmartHome implements SmartHomeMediator {
    
    private final String systemName;
    private final List<SmartDevice> devices;
    
    public ConcreteSmartHome(String systemName) {
        this.systemName = systemName;
        this.devices = new ArrayList<>();
    }
    
    @Override
    public void registerDevice(SmartDevice device) {
        devices.add(device);
        log.info("📱 设备 '{}' 已注册到智能家居系统", device.getDeviceName());
    }
    
    @Override
    public void activateScene(String sceneName) {
        log.info("🎬 激活场景模式: {}", sceneName);
        
        switch (sceneName) {
            case "回家模式" -> activateHomeScene();
            case "睡眠模式" -> activateSleepScene();
            case "观影模式" -> activateMovieScene();
            default -> log.warn("⚠️ 未知的场景模式: {}", sceneName);
        }
    }
    
    private void activateHomeScene() {
        log.info("🏠 执行回家模式设置:");
        for (SmartDevice device : devices) {
            switch (device.getDeviceName()) {
                case "客厅灯" -> {
                    device.turnOn();
                    device.setSetting("80");
                }
                case "空调" -> {
                    device.turnOn();
                    device.setSetting("26");
                }
                case "窗帘" -> {
                    device.turnOff(); // 关闭窗帘
                }
                case "安防系统" -> device.turnOff();
                case "音响系统" -> {
                    device.turnOn();
                    device.setSetting("轻音乐");
                }
            }
        }
    }
    
    private void activateSleepScene() {
        log.info("🌙 执行睡眠模式设置:");
        for (SmartDevice device : devices) {
            switch (device.getDeviceName()) {
                case "客厅灯" -> device.turnOff();
                case "空调" -> {
                    device.turnOn();
                    device.setSetting("22");
                }
                case "窗帘" -> device.turnOff(); // 关闭窗帘
                case "安防系统" -> device.turnOn();
                case "音响系统" -> device.turnOff();
            }
        }
    }
    
    private void activateMovieScene() {
        log.info("🎬 执行观影模式设置:");
        for (SmartDevice device : devices) {
            switch (device.getDeviceName()) {
                case "客厅灯" -> {
                    device.turnOn();
                    device.setSetting("20"); // 调暗灯光
                }
                case "空调" -> {
                    device.turnOn();
                    device.setSetting("24");
                }
                case "窗帘" -> device.turnOff(); // 关闭窗帘
                case "安防系统" -> device.turnOn();
                case "音响系统" -> {
                    device.turnOn();
                    device.setSetting("60"); // 调高音量
                }
            }
        }
    }
    
    @Override
    public void deviceStatusChanged(SmartDevice device, String status) {
        log.info("📊 设备状态变化: {} - {}", device.getDeviceName(), status);
        
        // 根据设备状态变化触发其他设备的联动
        if (device.getDeviceName().equals("安防系统") && status.contains("异常")) {
            log.info("🚨 检测到安全异常，启动应急模式:");
            
            // 打开所有灯光
            for (SmartDevice d : devices) {
                if (d.getDeviceName().contains("灯")) {
                    d.turnOn();
                    d.setSetting("100");
                }
            }
            
            // 关闭音响
            for (SmartDevice d : devices) {
                if (d.getDeviceName().contains("音响")) {
                    d.turnOff();
                }
            }
        }
    }
    
    @Override
    public void showSystemStatus() {
        log.info("📊 {} 状态报告:", systemName);
        log.info("   注册设备数: {}", devices.size());
        for (SmartDevice device : devices) {
            String status = device.isOn() ? "开启" : "关闭";
            log.info("     • {}: {}", device.getDeviceName(), status);
        }
    }
}
