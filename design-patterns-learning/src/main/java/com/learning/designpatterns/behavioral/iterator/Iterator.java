package com.learning.designpatterns.behavioral.iterator;

/**
 * 迭代器接口
 * 
 * 定义访问和遍历元素的接口
 * 
 * @param <T> 元素类型
 * <AUTHOR>
 */
public interface Iterator<T> {
    
    /**
     * 检查是否还有下一个元素
     * 
     * @return 如果还有下一个元素返回true，否则返回false
     */
    boolean hasNext();
    
    /**
     * 返回下一个元素
     * 
     * @return 下一个元素
     * @throws java.util.NoSuchElementException 如果没有更多元素
     */
    T next();
    
    /**
     * 重置迭代器到初始位置
     */
    void reset();
    
    /**
     * 获取当前位置
     * 
     * @return 当前位置索引
     */
    int getCurrentPosition();
}
