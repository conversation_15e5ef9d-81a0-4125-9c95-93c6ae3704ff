package com.learning.designpatterns.behavioral.mediator;

/**
 * 飞机抽象类
 * 
 * <AUTHOR>
 */
public abstract class Aircraft {
    
    protected String flightNumber;
    protected String airline;
    protected AirTrafficControlMediator mediator;
    
    public Aircraft(String flightNumber, String airline, AirTrafficControlMediator mediator) {
        this.flightNumber = flightNumber;
        this.airline = airline;
        this.mediator = mediator;
    }
    
    /**
     * 请求起飞
     */
    public abstract void requestTakeoff();
    
    /**
     * 请求降落
     */
    public abstract void requestLanding();
    
    /**
     * 请求紧急降落
     */
    public void requestEmergencyLanding() {
        mediator.requestEmergencyLanding(this);
    }
    
    /**
     * 接收塔台指令
     * 
     * @param instruction 指令内容
     */
    public abstract void receiveInstruction(String instruction);
    
    /**
     * 获取飞机标识
     * 
     * @return 飞机标识
     */
    public String getIdentifier() {
        return airline + " " + flightNumber;
    }
    
    public String getFlightNumber() {
        return flightNumber;
    }
    
    public String getAirline() {
        return airline;
    }
}
