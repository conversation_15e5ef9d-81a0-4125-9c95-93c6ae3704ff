package com.learning.designpatterns.behavioral.interpreter;

import lombok.extern.slf4j.Slf4j;

/**
 * 数字表达式（终结符表达式）
 * 
 * 表示一个具体的数字值
 * 
 * <AUTHOR>
 */
@Slf4j
public class NumberExpression implements Expression {
    
    private final int number;
    
    /**
     * 构造函数
     * 
     * @param number 数字值
     */
    public NumberExpression(int number) {
        this.number = number;
        log.info("🔢 创建数字表达式: {}", number);
    }
    
    @Override
    public int interpret(Context context) {
        log.info("🔢 解释数字: {}", number);
        return number;
    }
    
    @Override
    public String toString() {
        return String.valueOf(number);
    }
}
