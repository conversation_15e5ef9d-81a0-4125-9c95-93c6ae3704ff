package com.learning.designpatterns.behavioral.mediator;

import lombok.extern.slf4j.Slf4j;

/**
 * 商业客机类
 * 
 * <AUTHOR>
 */
@Slf4j
public class CommercialAircraft extends Aircraft {
    
    public CommercialAircraft(String flightNumber, String airline, AirTrafficControlMediator mediator) {
        super(flightNumber, airline, mediator);
    }
    
    @Override
    public void requestTakeoff() {
        log.info("✈️ 客机 {} 请求起飞许可", getIdentifier());
        mediator.requestTakeoff(this);
    }
    
    @Override
    public void requestLanding() {
        log.info("🛬 客机 {} 请求降落许可", getIdentifier());
        mediator.requestLanding(this);
    }
    
    @Override
    public void receiveInstruction(String instruction) {
        log.info("📻 客机 {} 收到塔台指令: {}", getIdentifier(), instruction);
    }
}
