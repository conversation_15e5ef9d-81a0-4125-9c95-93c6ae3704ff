package com.learning.designpatterns.behavioral.memento;

import lombok.extern.slf4j.Slf4j;

/**
 * 备忘录模式演示
 * 
 * 演示如何使用备忘录模式实现撤销/重做功能和游戏存档系统
 * 
 * 备忘录模式的应用场景：
 * 1. 文本编辑器的撤销/重做功能
 * 2. 游戏存档系统
 * 3. 数据库事务回滚
 * 4. 浏览器历史记录
 * 5. IDE的代码版本控制
 * 
 * <AUTHOR>
 */
@Slf4j
public class MementoDemo {
    
    public static void main(String[] args) {
        log.info("🎯 备忘录模式演示开始");
        log.info("=" .repeat(50));
        
        // 演示1: 文本编辑器撤销/重做
        demonstrateTextEditorUndo();
        
        log.info("");
        
        // 演示2: 游戏存档系统
        demonstrateGameSaveSystem();
        
        log.info("");
        
        // 演示3: 复杂的编辑操作
        demonstrateComplexEditing();
        
        log.info("");
        
        // 演示4: 游戏快速存档
        demonstrateQuickSave();
        
        log.info("=" .repeat(50));
        log.info("✅ 备忘录模式演示完成");
    }
    
    /**
     * 演示文本编辑器撤销/重做功能
     */
    private static void demonstrateTextEditorUndo() {
        log.info("📝 演示1: 文本编辑器撤销/重做");
        log.info("-" .repeat(30));
        
        TextEditor editor = new TextEditor("我的文档");
        EditorHistory history = new EditorHistory();
        
        // 初始状态
        editor.showStatus();
        history.save(editor.createMemento("初始状态"));
        
        log.info("");
        
        // 编辑操作1
        editor.write("Hello");
        editor.showStatus();
        history.save(editor.createMemento("写入Hello"));
        
        log.info("");
        
        // 编辑操作2
        editor.write(" World");
        editor.showStatus();
        history.save(editor.createMemento("写入World"));
        
        log.info("");
        
        // 编辑操作3
        editor.write("!");
        editor.showStatus();
        history.save(editor.createMemento("添加感叹号"));
        
        log.info("");
        history.showHistory();
        
        // 撤销操作
        log.info("");
        log.info("🔄 开始撤销操作:");
        
        Memento memento = history.undo();
        if (memento != null) {
            editor.restoreFromMemento(memento);
            editor.showStatus();
        }
        
        memento = history.undo();
        if (memento != null) {
            editor.restoreFromMemento(memento);
            editor.showStatus();
        }
        
        // 重做操作
        log.info("");
        log.info("🔄 开始重做操作:");
        
        memento = history.redo();
        if (memento != null) {
            editor.restoreFromMemento(memento);
            editor.showStatus();
        }
    }
    
    /**
     * 演示游戏存档系统
     */
    private static void demonstrateGameSaveSystem() {
        log.info("🎮 演示2: 游戏存档系统");
        log.info("-" .repeat(30));
        
        GameState game = new GameState("勇者小明");
        SaveManager saveManager = new SaveManager();
        
        // 初始状态
        game.showStatus();
        saveManager.saveGame(game.createSave("游戏开始"));
        
        log.info("");
        
        // 游戏进程1
        game.gainExperience(80);
        game.nextStage();
        game.buyItem(50, "铁剑");
        game.showStatus();
        saveManager.saveGame(game.createSave("第2关开始"));
        
        log.info("");
        
        // 游戏进程2
        game.gainExperience(120);
        game.takeDamage(30);
        game.useMana(20);
        game.nextStage();
        game.showStatus();
        saveManager.saveGame(game.createSave("第3关开始"));
        
        log.info("");
        
        // 游戏进程3
        game.gainExperience(150);
        game.buyItem(200, "魔法药水");
        game.heal(50);
        game.showStatus();
        saveManager.saveGame(game.createSave("购买药水后"));
        
        log.info("");
        saveManager.showAllSaves();
        
        // 加载之前的存档
        log.info("");
        log.info("🔄 加载第2关存档:");
        GameSave save = saveManager.loadGame(1);
        if (save != null) {
            game.loadFromSave(save);
            game.showStatus();
        }
    }
    
    /**
     * 演示复杂的编辑操作
     */
    private static void demonstrateComplexEditing() {
        log.info("📝 演示3: 复杂的编辑操作");
        log.info("-" .repeat(30));
        
        TextEditor editor = new TextEditor("代码文件");
        EditorHistory history = new EditorHistory();
        
        // 编写代码
        editor.write("public class Hello {");
        history.save(editor.createMemento("类声明"));
        
        editor.write("\n    public static void main(String[] args) {");
        history.save(editor.createMemento("main方法"));
        
        editor.write("\n        System.out.println(\"Hello World\");");
        history.save(editor.createMemento("打印语句"));
        
        editor.write("\n    }\n}");
        history.save(editor.createMemento("完成代码"));
        
        log.info("");
        editor.showStatus();
        
        log.info("");
        history.showHistory();
        
        // 修改代码
        log.info("");
        log.info("🔄 修改代码:");
        editor.replace("Hello World", "Hello Java");
        history.save(editor.createMemento("修改输出文本"));
        
        editor.showStatus();
        
        // 撤销到之前的版本
        log.info("");
        log.info("🔄 撤销到main方法版本:");
        Memento memento = history.jumpTo(1);
        if (memento != null) {
            editor.restoreFromMemento(memento);
            editor.showStatus();
        }
    }
    
    /**
     * 演示游戏快速存档
     */
    private static void demonstrateQuickSave() {
        log.info("⚡ 演示4: 游戏快速存档");
        log.info("-" .repeat(30));
        
        GameState game = new GameState("法师艾莉");
        SaveManager saveManager = new SaveManager();
        
        // 游戏进程
        game.gainExperience(200);
        game.nextStage();
        game.nextStage();
        game.buyItem(150, "法师袍");
        game.showStatus();
        
        // 快速存档到不同槽位
        log.info("");
        log.info("⚡ 创建快速存档:");
        saveManager.quickSave(1, game.createSave("BOSS战前"));
        
        // 继续游戏（BOSS战）
        game.takeDamage(60);
        game.useMana(30);
        game.gainExperience(300);
        game.showStatus();
        
        saveManager.quickSave(2, game.createSave("BOSS战后"));
        
        // 继续游戏
        game.nextStage();
        game.heal(40);
        game.buyItem(300, "传说武器");
        game.showStatus();
        
        saveManager.quickSave(3, game.createSave("获得传说武器"));
        
        log.info("");
        saveManager.showAllSaves();
        
        // 快速加载
        log.info("");
        log.info("⚡ 快速加载BOSS战前存档:");
        GameSave quickSave = saveManager.quickLoad(1);
        if (quickSave != null) {
            game.loadFromSave(quickSave);
            game.showStatus();
        }
        
        // 尝试不同的战斗策略
        log.info("");
        log.info("🎮 尝试不同的战斗策略:");
        game.useMana(40);
        game.takeDamage(20);
        game.gainExperience(300);
        game.showStatus();
        
        // 如果不满意，再次加载
        log.info("");
        log.info("⚡ 重新加载尝试其他策略:");
        quickSave = saveManager.quickLoad(1);
        if (quickSave != null) {
            game.loadFromSave(quickSave);
            game.showStatus();
        }
    }
}
