@echo off
echo 正在测试项目编译...

echo.
echo ========================================
echo 测试 Spring Security 项目编译
echo ========================================
cd spring-security-auth
call mvn clean compile -DskipTests
if %ERRORLEVEL% neq 0 (
    echo Spring Security 项目编译失败！
    cd ..
    pause
    exit /b 1
)
echo Spring Security 项目编译成功！
cd ..

echo.
echo ========================================
echo 测试 Sa-Token 项目编译
echo ========================================
cd sa-token-auth
call mvn clean compile -DskipTests
if %ERRORLEVEL% neq 0 (
    echo Sa-Token 项目编译失败！
    cd ..
    pause
    exit /b 1
)
echo Sa-Token 项目编译成功！
cd ..

echo.
echo ========================================
echo 所有项目编译成功！
echo ========================================
echo.
echo 接下来可以启动项目：
echo 1. Spring Security 项目: cd spring-security-auth && mvn spring-boot:run
echo 2. Sa-Token 项目: cd sa-token-auth && mvn spring-boot:run
echo.
pause
