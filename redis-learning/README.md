# Redis Learning Module

这个模块用于学习和实践Redis相关的功能和特性。

## 功能特性

1. Redis基础操作
   - 字符串(String)操作
   - 列表(List)操作
   - 集合(Set)操作
   - 有序集合(Sorted Set)操作
   - 哈希(Hash)操作

2. Redis进阶特性
   - 发布订阅
   - 事务操作
   - 缓存策略
   - 分布式锁

## 如何使用

1. 确保本地安装了Redis服务器，或使用远程Redis服务器
2. 在application.yml中配置Redis连接信息
3. 运行示例代码学习不同的Redis特性

### 演示配置

可以通过设置Spring profile为`demo`来运行内置的演示程序：

```bash
# 命令行运行
java -jar redis-learning.jar --spring.profiles.active=demo
```

或者在`application.yml`中设置：

```yaml
spring:
  profiles:
    active: demo
```

## REST API

该模块提供了一组REST API来演示Redis的各种功能：

### 字符串操作

- `GET /redis/string/{key}` - 获取字符串值
- `POST /redis/string/{key}` - 设置字符串值
- `DELETE /redis/string/{key}` - 删除字符串值
- `PUT /redis/string/{key}/increment` - 增加字符串值

### 列表操作

- `GET /redis/list/{key}` - 获取列表所有值
- `POST /redis/list/{key}` - 向列表添加元素
- `DELETE /redis/list/{key}/pop` - 从列表弹出元素

### 集合操作

- `GET /redis/set/{key}` - 获取集合所有成员
- `POST /redis/set/{key}` - 向集合添加元素
- `DELETE /redis/set/{key}` - 从集合移除元素

### 分布式锁操作

- `POST /redis/lock/{key}` - 获取锁
- `DELETE /redis/lock/{key}` - 释放锁

### 缓存操作

- `GET /redis/cache/{key}` - 从缓存获取值
- `POST /redis/cache/{key}` - 向缓存存入值
- `DELETE /redis/cache/{key}` - 从缓存删除值

## 示例代码结构

- `com.example.config`: Redis配置类
  - `RedisConfig` - Redis模板配置
  - `RedisListenerConfig` - Redis消息监听器配置
- `com.example.service`: Redis操作服务接口
  - `StringRedisService` - 字符串操作
  - `ListRedisService` - 列表操作
  - `SetRedisService` - 集合操作
  - `ZSetRedisService` - 有序集合操作
  - `HashRedisService` - 哈希操作
  - `PubSubService` - 发布订阅
  - `RedisTransactionService` - 事务操作
  - `RedisCacheService` - 缓存策略
  - `RedisLockService` - 分布式锁
- `com.example.service.impl`: 服务实现类
- `com.example.controller`: REST API接口
  - `RedisController` - Redis操作REST接口
  - `TestController` - 测试接口
- `com.example.demo`: 各种Redis特性的示例代码
  - `RedisBasicDemo` - 基础操作示例
  - `RedisAdvancedDemo` - 进阶特性示例

## 学习路径

1. 从基础的CRUD操作开始（`StringRedisService`等）
2. 学习不同数据类型的使用场景
3. 实践缓存策略（`RedisCacheService`）
4. 探索分布式特性（`RedisLockService`和`PubSubService`）

## Docker支持

项目提供了Docker支持，可以通过以下命令构建和运行：

```bash
# 构建Docker镜像
mvn clean package
docker build -t redis-learning --build-arg JAR_FILE=target/redis-learning.jar .

# 运行容器
docker run -p 8081:8081 redis-learning
```

## 配置说明

在`application.yml`中可以配置Redis连接信息：

```yaml
spring:
  data:
    redis:
      host: *************  # Redis服务器地址
      port: 6379           # Redis端口
      database: 0          # 使用的数据库索引
      timeout: 60000       # 连接超时时间
      client-type: lettuce # 客户端类型
      lettuce:
        pool:
          max-active: 8    # 连接池最大连接数
          max-wait: -1     # 连接池最大阻塞等待时间
          max-idle: 8      # 连接池中的最大空闲连接
          min-idle: 0      # 连接池中的最小空闲连接
``` 