package com.example.demo;

import com.example.service.PubSubService;
import com.example.service.RedisCacheService;
import com.example.service.RedisLockService;
import com.example.service.RedisTransactionService;
import com.example.service.impl.RedisLockServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis进阶特性示例
 *
 * 仅在开发环境中运行
 */
@Slf4j
@Component
@Profile("demo")  // 仅在demo配置激活时运行
public class RedisAdvancedDemo implements CommandLineRunner {

    private final PubSubService pubSubService;
    private final RedisTransactionService transactionService;
    private final RedisCacheService cacheService;
    private final RedisLockService lockService;

    @Autowired
    public RedisAdvancedDemo(PubSubService pubSubService,
                            RedisTransactionService transactionService,
                            RedisCacheService cacheService,
                            RedisLockService lockService) {
        this.pubSubService = pubSubService;
        this.transactionService = transactionService;
        this.cacheService = cacheService;
        this.lockService = lockService;
    }

    @Override
    public void run(String... args) {
        log.info("开始执行Redis进阶特性示例...");
        
        try {
            demoPubSub();
            demoTransaction();
            demoCache();
            demoDistributedLock();
            
            log.info("Redis进阶特性示例执行完成！");
        } catch (Exception e) {
            log.error("执行Redis进阶特性示例时发生错误", e);
        }
    }
    
    private void demoPubSub() throws Exception {
        log.info("--- 发布订阅示例 ---");
        
        CountDownLatch latch = new CountDownLatch(2); // 等待两条消息
        
        // 创建一个简单的消息监听器
        MessageListener listener = new MessageListener() {
            @Override
            public void onMessage(Message message, byte[] pattern) {
                String channel = new String(message.getChannel());
                String body = new String(message.getBody());
                log.info("收到消息 - 通道: {}, 内容: {}", channel, body);
                latch.countDown();
            }
        };
        
        // 订阅两个通道
        pubSubService.subscribe(listener, "demo:channel:news", "demo:channel:events");
        
        // 发布消息
        pubSubService.publish("demo:channel:news", "这是一条新闻消息");
        pubSubService.publish("demo:channel:events", "这是一条事件消息");
        
        // 等待消息接收
        latch.await(5, TimeUnit.SECONDS);
        
        // 取消订阅
        pubSubService.unsubscribe(listener, "demo:channel:news", "demo:channel:events");
        
        log.info("发布订阅示例完成");
    }
    
    private void demoTransaction() {
        log.info("--- 事务操作示例 ---");
        
        // 简单事务：批量设置多个键值对
        List<Object> result = transactionService.simpleTransaction(
                "demo:tx:key1", "值1",
                "demo:tx:key2", "值2",
                "demo:tx:key3", "值3"
        );
        log.info("简单事务执行结果: {}", result);
        
        // 条件事务示例：仅当键的值等于期望值时，才设置新值
        String key = "demo:tx:conditional";
        transactionService.watch(key); // 监视键
        transactionService.simpleTransaction(key, "初始值");
        
        boolean success1 = transactionService.conditionalTransaction(key, "初始值", "新值");
                 Object[] results = transactionService.simpleTransaction(key, null).toArray();
         log.info("条件事务执行结果1: {}, 新值: {}", success1, results[0]);
        
        boolean success2 = transactionService.conditionalTransaction(key, "错误的期望值", "更新的值");
        log.info("条件事务执行结果2: {}", success2);
        
        transactionService.unwatch(); // 取消监视
        
        log.info("事务操作示例完成");
    }
    
    private void demoCache() {
        log.info("--- 缓存策略示例 ---");
        
        String key = "demo:cache:user:1001";
        
        // 清除缓存，以便演示
        cacheService.removeCache(key);
        
        // 创建一个模拟的数据加载器，假设是从数据库加载数据
        Supplier<User> dataLoader = () -> {
            log.info("从数据源加载用户数据...");
            try {
                Thread.sleep(100); // 模拟数据库查询延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return new User(1001L, "张三", "<EMAIL>");
        };
        
        // 第一次从缓存获取，会触发从数据源加载
        User user1 = cacheService.getWithCache(key, dataLoader, 30, TimeUnit.SECONDS);
        log.info("第一次获取（应该从数据源加载）: {}", user1);
        
        // 第二次从缓存获取，应该直接返回缓存中的数据
        User user2 = cacheService.getWithCache(key, dataLoader, 30, TimeUnit.SECONDS);
        log.info("第二次获取（应该从缓存返回）: {}", user2);
        
        // 清除缓存
        cacheService.removeCache(key);
        log.info("已清除缓存");
        
        // 使用带锁的缓存获取，防止缓存击穿
        String lockKey = "demo:cache:lock:user:1001";
        User user3 = cacheService.getWithLock(key, lockKey, dataLoader, 30, TimeUnit.SECONDS);
        log.info("使用锁机制获取（应该从数据源加载）: {}", user3);
        
        log.info("缓存策略示例完成");
    }
    
    private void demoDistributedLock() throws Exception {
        log.info("--- 分布式锁示例 ---");
        
        String lockKey = "demo:lock:resource";
        int threadCount = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.execute(() -> {
                String lockValue = RedisLockServiceImpl.generateLockValue();
                log.info("线程 {} 尝试获取锁", threadId);
                
                try {
                    boolean success = lockService.tryLock(lockKey, lockValue, 10, TimeUnit.SECONDS);
                    
                    if (success) {
                        log.info("线程 {} 获取锁成功", threadId);
                        try {
                            // 模拟执行一些操作
                            Thread.sleep(1000);
                            log.info("线程 {} 执行受保护的操作", threadId);
                        } finally {
                            // 释放锁
                            lockService.releaseLock(lockKey, lockValue);
                            log.info("线程 {} 释放锁", threadId);
                        }
                    } else {
                        log.info("线程 {} 获取锁失败", threadId);
                    }
                } catch (Exception e) {
                    log.error("线程 {} 发生异常", threadId, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        latch.await();
        executor.shutdown();
        
        // 演示使用锁执行操作
        String lockValue = UUID.randomUUID().toString();
        String result = lockService.executeWithLock(
                lockKey, lockValue, 10, TimeUnit.SECONDS,
                () -> {
                    log.info("在锁保护下执行操作");
                    return "操作成功";
                });
        log.info("使用锁执行操作的结果: {}", result);
        
        log.info("分布式锁示例完成");
    }
    
    // 用于缓存示例的简单用户类
    public static class User {
        private final Long id;
        private final String name;
        private final String email;
        
        public User(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }
        
        @Override
        public String toString() {
            return "User{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", email='" + email + '\'' +
                    '}';
        }
    }
} 