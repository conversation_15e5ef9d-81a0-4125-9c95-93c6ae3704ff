package com.example.demo;

import com.example.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis基本操作示例
 * 
 * 仅在开发环境中运行
 */
@Slf4j
@Component
@Profile("demo")  // 仅在demo配置激活时运行
public class RedisBasicDemo implements CommandLineRunner {

    private final StringRedisService stringRedisService;
    private final ListRedisService listRedisService;
    private final SetRedisService setRedisService;
    private final ZSetRedisService zSetRedisService;
    private final HashRedisService hashRedisService;

    @Autowired
    public RedisBasicDemo(StringRedisService stringRedisService,
                          ListRedisService listRedisService,
                          SetRedisService setRedisService,
                          ZSetRedisService zSetRedisService,
                          HashRedisService hashRedisService) {
        this.stringRedisService = stringRedisService;
        this.listRedisService = listRedisService;
        this.setRedisService = setRedisService;
        this.zSetRedisService = zSetRedisService;
        this.hashRedisService = hashRedisService;
    }

    @Override
    public void run(String... args) {
        log.info("开始执行Redis基本操作示例...");
        
        try {
            demoStringOperations();
            demoListOperations();
            demoSetOperations();
            demoZSetOperations();
            demoHashOperations();
            
            log.info("Redis基本操作示例执行完成！");
        } catch (Exception e) {
            log.error("执行Redis基本操作示例时发生错误", e);
        }
    }
    
    private void demoStringOperations() {
        log.info("--- 字符串操作示例 ---");
        
        String key = "demo:string:name";
        stringRedisService.set(key, "Redis学习示例");
        log.info("设置字符串值: {}", stringRedisService.get(key));
        
        key = "demo:string:counter";
        stringRedisService.set(key, 0);
        log.info("递增前: {}", stringRedisService.get(key));
        stringRedisService.increment(key, 5);
        log.info("递增后: {}", stringRedisService.get(key));
        
        key = "demo:string:expirable";
        stringRedisService.set(key, "这个值将在10秒后过期", 10, TimeUnit.SECONDS);
        log.info("设置带过期时间的值: {}", stringRedisService.get(key));
        log.info("剩余时间: {} 秒", stringRedisService.getExpire(key, TimeUnit.SECONDS));
    }
    
    private void demoListOperations() {
        log.info("--- 列表操作示例 ---");
        
        String key = "demo:list:tasks";
        // 清除可能存在的旧值
        stringRedisService.delete(key);
        
        listRedisService.rightPush(key, "任务1");
        listRedisService.rightPush(key, "任务2");
        listRedisService.rightPush(key, "任务3");
        listRedisService.leftPush(key, "任务0");
        
        log.info("列表大小: {}", listRedisService.size(key));
        log.info("所有元素: {}", listRedisService.range(key, 0, -1));
        
        log.info("弹出左侧元素: {}", listRedisService.leftPop(key));
        log.info("弹出后的列表: {}", listRedisService.range(key, 0, -1));
        
        log.info("获取索引为1的元素: {}", listRedisService.index(key, 1));
        
        listRedisService.set(key, 0, "已修改的任务1");
        log.info("修改后的列表: {}", listRedisService.range(key, 0, -1));
    }
    
    private void demoSetOperations() {
        log.info("--- 集合操作示例 ---");
        
        String key1 = "demo:set:tags1";
        String key2 = "demo:set:tags2";
        
        setRedisService.remove(key1, "Java", "Python", "Go", "Redis");
        setRedisService.remove(key2, "Java", "C++", "Redis", "MongoDB");
        
        setRedisService.add(key1, "Java", "Python", "Go", "Redis");
        setRedisService.add(key2, "Java", "C++", "Redis", "MongoDB");
        
        log.info("集合1: {}", setRedisService.members(key1));
        log.info("集合2: {}", setRedisService.members(key2));
        
        log.info("判断元素是否存在: {}", setRedisService.isMember(key1, "Java"));
        
        log.info("集合1和集合2的交集: {}", setRedisService.intersect(key1, key2));
        log.info("集合1和集合2的并集: {}", setRedisService.union(key1, key2));
        log.info("集合1和集合2的差集: {}", setRedisService.difference(key1, key2));
        
        log.info("随机获取集合1中的一个元素: {}", setRedisService.randomMember(key1));
        log.info("随机获取集合1中的两个元素: {}", setRedisService.randomMembers(key1, 2));
    }
    
    private void demoZSetOperations() {
        log.info("--- 有序集合操作示例 ---");
        
        String key = "demo:zset:scores";
        
        zSetRedisService.remove(key, "张三", "李四", "王五", "赵六");
        
        zSetRedisService.add(key, "张三", 95.5);
        zSetRedisService.add(key, "李四", 89.0);
        zSetRedisService.add(key, "王五", 97.5);
        zSetRedisService.add(key, "赵六", 82.0);
        
        log.info("所有成员: {}", zSetRedisService.range(key, 0, -1));
        log.info("得分在85-95之间的成员: {}", zSetRedisService.rangeByScore(key, 85, 95));
        
        log.info("张三的分数: {}", zSetRedisService.score(key, "张三"));
        
        zSetRedisService.incrementScore(key, "张三", 2.0);
        log.info("增加分数后的张三分数: {}", zSetRedisService.score(key, "张三"));
        
        log.info("按分数从高到低排名: {}", zSetRedisService.rank(key, "张三"));
        log.info("集合大小: {}", zSetRedisService.size(key));
        log.info("分数在90以上的成员数量: {}", zSetRedisService.count(key, 90, Double.MAX_VALUE));
    }
    
    private void demoHashOperations() {
        log.info("--- 哈希操作示例 ---");
        
        String key = "demo:hash:user:1";
        
        hashRedisService.delete(key, "id", "name", "age", "email");
        
        hashRedisService.put(key, "id", "1001");
        hashRedisService.put(key, "name", "张三");
        hashRedisService.put(key, "age", 28);
        hashRedisService.put(key, "email", "<EMAIL>");
        
        log.info("所有字段: {}", hashRedisService.keys(key));
        log.info("所有值: {}", hashRedisService.values(key));
        log.info("所有字段和值: {}", hashRedisService.entries(key));
        
        log.info("获取name字段: {}", hashRedisService.get(key, "name"));
        log.info("判断字段是否存在: {}", hashRedisService.hasKey(key, "age"));
        
        hashRedisService.increment(key, "age", 2);
        log.info("递增后的age: {}", hashRedisService.get(key, "age"));
        
        Map<Object, Object> userData = new HashMap<>();
        userData.put("address", "北京市");
        userData.put("phone", "13800138000");
        
        hashRedisService.putAll(key, userData);
        log.info("添加多个字段后的所有数据: {}", hashRedisService.entries(key));
    }
} 