package com.example.controller;

import com.example.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis操作控制器
 */
@Slf4j
@RestController
@RequestMapping("/redis")
public class RedisController {

    private final StringRedisService stringRedisService;
    private final ListRedisService listRedisService;
    private final SetRedisService setRedisService;
    private final ZSetRedisService zSetRedisService;
    private final HashRedisService hashRedisService;
    private final RedisLockService lockService;
    private final RedisCacheService cacheService;

    @Autowired
    public RedisController(StringRedisService stringRedisService,
                          ListRedisService listRedisService,
                          SetRedisService setRedisService,
                          ZSetRedisService zSetRedisService,
                          HashRedisService hashRedisService,
                          RedisLockService lockService,
                          RedisCacheService cacheService) {
        this.stringRedisService = stringRedisService;
        this.listRedisService = listRedisService;
        this.setRedisService = setRedisService;
        this.zSetRedisService = zSetRedisService;
        this.hashRedisService = hashRedisService;
        this.lockService = lockService;
        this.cacheService = cacheService;
    }

    // ================ 字符串操作 ================

    @GetMapping("/string/{key}")
    public Object getStringValue(@PathVariable String key) {
        return stringRedisService.get(key);
    }

    @PostMapping("/string/{key}")
    public Map<String, Object> setStringValue(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Object value = payload.get("value");
        Integer expireSeconds = (Integer) payload.get("expireSeconds");
        
        if (expireSeconds != null && expireSeconds > 0) {
            stringRedisService.set(key, value, expireSeconds, TimeUnit.SECONDS);
        } else {
            stringRedisService.set(key, value);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("value", value);
        result.put("expire", expireSeconds);
        result.put("operation", "set");
        return result;
    }

    @DeleteMapping("/string/{key}")
    public Map<String, Object> deleteStringValue(@PathVariable String key) {
        boolean success = stringRedisService.delete(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("success", success);
        result.put("operation", "delete");
        return result;
    }

    @PutMapping("/string/{key}/increment")
    public Map<String, Object> incrementStringValue(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Long delta = Long.valueOf(payload.get("delta").toString());
        Long newValue = stringRedisService.increment(key, delta);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("delta", delta);
        result.put("newValue", newValue);
        result.put("operation", "increment");
        return result;
    }

    // ================ 列表操作 ================

    @GetMapping("/list/{key}")
    public Map<String, Object> getListValues(@PathVariable String key) {
        List<Object> values = listRedisService.range(key, 0, -1);
        Long size = listRedisService.size(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("values", values);
        result.put("size", size);
        return result;
    }

    @PostMapping("/list/{key}")
    public Map<String, Object> addToList(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Object value = payload.get("value");
        String position = (String) payload.getOrDefault("position", "right");
        
        Long newSize;
        if ("left".equalsIgnoreCase(position)) {
            newSize = listRedisService.leftPush(key, value);
        } else {
            newSize = listRedisService.rightPush(key, value);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("value", value);
        result.put("position", position);
        result.put("newSize", newSize);
        result.put("operation", "push");
        return result;
    }

    @DeleteMapping("/list/{key}/pop")
    public Map<String, Object> popFromList(@PathVariable String key, @RequestParam(defaultValue = "right") String position) {
        Object value;
        if ("left".equalsIgnoreCase(position)) {
            value = listRedisService.leftPop(key);
        } else {
            value = listRedisService.rightPop(key);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("value", value);
        result.put("position", position);
        result.put("operation", "pop");
        return result;
    }

    // ================ 集合操作 ================

    @GetMapping("/set/{key}")
    public Map<String, Object> getSetMembers(@PathVariable String key) {
        Set<Object> members = setRedisService.members(key);
        Long size = setRedisService.size(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("members", members);
        result.put("size", size);
        return result;
    }

    @PostMapping("/set/{key}")
    public Map<String, Object> addToSet(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Object[] values = ((java.util.List<?>) payload.get("values")).toArray();
        Long addedCount = setRedisService.add(key, values);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("values", values);
        result.put("addedCount", addedCount);
        result.put("operation", "add");
        return result;
    }

    @DeleteMapping("/set/{key}")
    public Map<String, Object> removeFromSet(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Object[] values = ((java.util.List<?>) payload.get("values")).toArray();
        Long removedCount = setRedisService.remove(key, values);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("values", values);
        result.put("removedCount", removedCount);
        result.put("operation", "remove");
        return result;
    }

    // ================ 分布式锁操作 ================

    @PostMapping("/lock/{key}")
    public Map<String, Object> acquireLock(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Integer timeoutSeconds = (Integer) payload.getOrDefault("timeout", 30);
        String value = UUID.randomUUID().toString();
        
        boolean acquired = lockService.tryLock(key, value, timeoutSeconds, TimeUnit.SECONDS);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("value", value);
        result.put("acquired", acquired);
        result.put("timeout", timeoutSeconds);
        result.put("operation", "tryLock");
        return result;
    }

    @DeleteMapping("/lock/{key}")
    public Map<String, Object> releaseLock(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        String value = (String) payload.get("value");
        boolean released = lockService.releaseLock(key, value);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("released", released);
        result.put("operation", "releaseLock");
        return result;
    }

    // ================ 缓存操作 ================

    @GetMapping("/cache/{key}")
    public Map<String, Object> getFromCache(@PathVariable String key) {
        Object value = cacheService.getFromCache(key);
        boolean exists = cacheService.hasKey(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("value", value);
        result.put("exists", exists);
        result.put("operation", "getFromCache");
        return result;
    }

    @PostMapping("/cache/{key}")
    public Map<String, Object> putToCache(@PathVariable String key, @RequestBody Map<String, Object> payload) {
        Object value = payload.get("value");
        Integer expireSeconds = (Integer) payload.getOrDefault("expireSeconds", 300);
        
        boolean success = cacheService.putCache(key, value, expireSeconds, TimeUnit.SECONDS);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("success", success);
        result.put("expireSeconds", expireSeconds);
        result.put("operation", "putCache");
        return result;
    }

    @DeleteMapping("/cache/{key}")
    public Map<String, Object> removeFromCache(@PathVariable String key) {
        boolean success = cacheService.removeCache(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("key", key);
        result.put("success", success);
        result.put("operation", "removeCache");
        return result;
    }
} 