package com.example.service;

import java.util.List;

/**
 * Redis列表操作服务接口
 */
public interface ListRedisService {
    
    /**
     * 从列表左侧添加元素
     * 
     * @param key 键
     * @param value 值
     * @return 添加后列表的长度
     */
    Long leftPush(String key, Object value);
    
    /**
     * 从列表左侧批量添加元素
     * 
     * @param key 键
     * @param values 值集合
     * @return 添加后列表的长度
     */
    Long leftPushAll(String key, Object... values);
    
    /**
     * 从列表右侧添加元素
     * 
     * @param key 键
     * @param value 值
     * @return 添加后列表的长度
     */
    Long rightPush(String key, Object value);
    
    /**
     * 从列表右侧批量添加元素
     * 
     * @param key 键
     * @param values 值集合
     * @return 添加后列表的长度
     */
    Long rightPushAll(String key, Object... values);
    
    /**
     * 从列表左侧弹出元素
     * 
     * @param key 键
     * @return 弹出的元素
     */
    Object leftPop(String key);
    
    /**
     * 从列表右侧弹出元素
     * 
     * @param key 键
     * @return 弹出的元素
     */
    Object rightPop(String key);
    
    /**
     * 获取列表指定范围内的元素
     * 
     * @param key 键
     * @param start 开始索引
     * @param end 结束索引
     * @return 元素列表
     */
    List<Object> range(String key, long start, long end);
    
    /**
     * 获取列表长度
     * 
     * @param key 键
     * @return 列表长度
     */
    Long size(String key);
    
    /**
     * 通过索引获取列表中的元素
     * 
     * @param key 键
     * @param index 索引
     * @return 元素
     */
    Object index(String key, long index);
    
    /**
     * 在列表的指定位置设置元素
     * 
     * @param key 键
     * @param index 索引
     * @param value 值
     */
    void set(String key, long index, Object value);
    
    /**
     * 从列表中删除指定值的元素
     * 
     * @param key 键
     * @param count 计数
     *              count > 0: 从左到右删除count个指定元素
     *              count < 0: 从右到左删除count个指定元素
     *              count = 0: 删除所有指定元素
     * @param value 值
     * @return 删除的元素个数
     */
    Long remove(String key, long count, Object value);
} 