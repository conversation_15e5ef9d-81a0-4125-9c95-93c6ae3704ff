package com.example.service;

import java.util.concurrent.TimeUnit;

/**
 * Redis字符串操作服务接口
 */
public interface StringRedisService {
    
    /**
     * 设置键值对
     * 
     * @param key 键
     * @param value 值
     */
    void set(String key, Object value);
    
    /**
     * 设置键值对及过期时间
     * 
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit unit);
    
    /**
     * 获取值
     * 
     * @param key 键
     * @return 值
     */
    Object get(String key);
    
    /**
     * 删除键
     * 
     * @param key 键
     * @return 是否成功
     */
    boolean delete(String key);
    
    /**
     * 检查键是否存在
     * 
     * @param key 键
     * @return 是否存在
     */
    boolean hasKey(String key);
    
    /**
     * 设置过期时间
     * 
     * @param key 键
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return 是否成功
     */
    boolean expire(String key, long timeout, TimeUnit unit);
    
    /**
     * 获取键的过期时间
     * 
     * @param key 键
     * @param unit 时间单位
     * @return 过期时间
     */
    long getExpire(String key, TimeUnit unit);
    
    /**
     * 将键的值增加给定的数值
     * 
     * @param key 键
     * @param delta 增加的数值
     * @return 增加后的值
     */
    Long increment(String key, long delta);
    
    /**
     * 将键的值减少给定的数值
     * 
     * @param key 键
     * @param delta 减少的数值
     * @return 减少后的值
     */
    Long decrement(String key, long delta);
} 