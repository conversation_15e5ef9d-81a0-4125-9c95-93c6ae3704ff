package com.example.service;

import org.springframework.data.redis.core.SessionCallback;

import java.util.List;

/**
 * Redis事务操作服务接口
 */
public interface RedisTransactionService {
    
    /**
     * 在一个事务中执行多个操作
     * 
     * @param callback 会话回调
     * @param <T> 返回类型
     * @return 执行结果
     */
    <T> T executeInTransaction(SessionCallback<T> callback);
    
    /**
     * 监视一个或多个键，当键被其他客户端修改时，事务将失败
     * 
     * @param keys 键列表
     */
    void watch(String... keys);
    
    /**
     * 取消对所有键的监视
     */
    void unwatch();
    
    /**
     * 执行一个简单的Redis事务示例：设置多个键值对
     * 
     * @param keysAndValues 键值对参数，格式为：key1, value1, key2, value2, ...
     * @return 事务执行结果
     */
    List<Object> simpleTransaction(Object... keysAndValues);
    
    /**
     * 带条件执行的事务：当key的值等于expectedValue时，才设置新值
     * 
     * @param key 键
     * @param expectedValue 期望的值
     * @param newValue 新值
     * @return 是否成功执行
     */
    boolean conditionalTransaction(String key, Object expectedValue, Object newValue);
} 