package com.example.service;

import org.springframework.data.redis.connection.MessageListener;

/**
 * Redis发布订阅服务接口
 */
public interface PubSubService {
    
    /**
     * 发布消息到指定通道
     * 
     * @param channel 通道
     * @param message 消息
     */
    void publish(String channel, Object message);
    
    /**
     * 订阅指定通道
     * 
     * @param listener 消息监听器
     * @param channels 通道列表
     */
    void subscribe(MessageListener listener, String... channels);
    
    /**
     * 取消订阅指定通道
     * 
     * @param listener 消息监听器
     * @param channels 通道列表
     */
    void unsubscribe(MessageListener listener, String... channels);
    
    /**
     * 模式订阅
     * 
     * @param listener 消息监听器
     * @param patterns 模式列表
     */
    void pSubscribe(MessageListener listener, String... patterns);
    
    /**
     * 取消模式订阅
     * 
     * @param listener 消息监听器
     * @param patterns 模式列表
     */
    void pUnsubscribe(MessageListener listener, String... patterns);
} 