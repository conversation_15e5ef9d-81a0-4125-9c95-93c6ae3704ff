package com.example.service;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis缓存服务接口
 */
public interface RedisCacheService {
    
    /**
     * 从缓存中获取数据，如果缓存中不存在，则通过数据加载器加载并存入缓存
     * 
     * @param key 键
     * @param dataLoader 数据加载器
     * @param <T> 数据类型
     * @return 数据
     */
    <T> T getWithCache(String key, Supplier<T> dataLoader);
    
    /**
     * 从缓存中获取数据，如果缓存中不存在，则通过数据加载器加载并存入缓存，并设置过期时间
     * 
     * @param key 键
     * @param dataLoader 数据加载器
     * @param timeout 过期时间
     * @param unit 时间单位
     * @param <T> 数据类型
     * @return 数据
     */
    <T> T getWithCache(String key, Supplier<T> dataLoader, long timeout, TimeUnit unit);
    
    /**
     * 将数据放入缓存
     * 
     * @param key 键
     * @param value 值
     * @param <T> 数据类型
     * @return 是否成功
     */
    <T> boolean putCache(String key, T value);
    
    /**
     * 将数据放入缓存，并设置过期时间
     * 
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param unit 时间单位
     * @param <T> 数据类型
     * @return 是否成功
     */
    <T> boolean putCache(String key, T value, long timeout, TimeUnit unit);
    
    /**
     * 从缓存中获取数据
     * 
     * @param key 键
     * @param <T> 数据类型
     * @return 数据
     */
    <T> T getFromCache(String key);
    
    /**
     * 从缓存中删除数据
     * 
     * @param key 键
     * @return 是否成功
     */
    boolean removeCache(String key);
    
    /**
     * 检查缓存中是否存在指定键
     * 
     * @param key 键
     * @return 是否存在
     */
    boolean hasKey(String key);
    
    /**
     * 使用缓存锁防止缓存击穿，从缓存获取数据，不存在则加载并存入缓存
     * 
     * @param key 缓存键
     * @param lockKey 锁键
     * @param dataLoader 数据加载器
     * @param timeout 过期时间
     * @param unit 时间单位
     * @param <T> 数据类型
     * @return 数据
     */
    <T> T getWithLock(String key, String lockKey, Supplier<T> dataLoader, long timeout, TimeUnit unit);
} 