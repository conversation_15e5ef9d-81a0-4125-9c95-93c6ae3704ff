package com.example.service.impl;

import com.example.service.PubSubService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Redis发布订阅服务实现类
 */
@Slf4j
@Service
public class PubSubServiceImpl implements PubSubService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisMessageListenerContainer listenerContainer;

    public PubSubServiceImpl(RedisTemplate<String, Object> redisTemplate, 
                            RedisMessageListenerContainer listenerContainer) {
        this.redisTemplate = redisTemplate;
        this.listenerContainer = listenerContainer;
    }

    @Override
    public void publish(String channel, Object message) {
        try {
            redisTemplate.convertAndSend(channel, message);
            log.debug("Redis发布消息到指定通道成功: channel={}", channel);
        } catch (Exception e) {
            log.error("Redis发布消息到指定通道失败: channel={}, error={}", 
                    channel, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void subscribe(MessageListener listener, String... channels) {
        try {
            List<Topic> topics = new ArrayList<>(channels.length);
            for (String channel : channels) {
                topics.add(new ChannelTopic(channel));
            }
            listenerContainer.addMessageListener(listener, topics);
            log.debug("Redis订阅指定通道成功: channels={}", Arrays.toString(channels));
        } catch (Exception e) {
            log.error("Redis订阅指定通道失败: channels={}, error={}", 
                    Arrays.toString(channels), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void unsubscribe(MessageListener listener, String... channels) {
        try {
            for (String channel : channels) {
                listenerContainer.removeMessageListener(listener, new ChannelTopic(channel));
            }
            log.debug("Redis取消订阅指定通道成功: channels={}", Arrays.toString(channels));
        } catch (Exception e) {
            log.error("Redis取消订阅指定通道失败: channels={}, error={}", 
                    Arrays.toString(channels), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void pSubscribe(MessageListener listener, String... patterns) {
        try {
            List<Topic> topics = new ArrayList<>(patterns.length);
            for (String pattern : patterns) {
                topics.add(new PatternTopic(pattern));
            }
            listenerContainer.addMessageListener(listener, topics);
            log.debug("Redis模式订阅成功: patterns={}", Arrays.toString(patterns));
        } catch (Exception e) {
            log.error("Redis模式订阅失败: patterns={}, error={}", 
                    Arrays.toString(patterns), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void pUnsubscribe(MessageListener listener, String... patterns) {
        try {
            for (String pattern : patterns) {
                listenerContainer.removeMessageListener(listener, new PatternTopic(pattern));
            }
            log.debug("Redis取消模式订阅成功: patterns={}", Arrays.toString(patterns));
        } catch (Exception e) {
            log.error("Redis取消模式订阅失败: patterns={}, error={}", 
                    Arrays.toString(patterns), e.getMessage(), e);
            throw e;
        }
    }
} 