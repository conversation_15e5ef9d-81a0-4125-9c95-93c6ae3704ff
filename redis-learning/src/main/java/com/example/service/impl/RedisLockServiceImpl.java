package com.example.service.impl;

import com.example.service.RedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis分布式锁服务实现类
 */
@Slf4j
@Service
public class RedisLockServiceImpl implements RedisLockService {

    private final RedisTemplate<String, Object> redisTemplate;
    private DefaultRedisScript<Long> releaseLockScript;
    
    private static final String RELEASE_LOCK_SCRIPT = 
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('del', KEYS[1]) " +
            "else " +
            "    return 0 " +
            "end";

    public RedisLockServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    @PostConstruct
    public void init() {
        releaseLockScript = new DefaultRedisScript<>();
        releaseLockScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("script/release_lock.lua")));
        releaseLockScript.setResultType(Long.class);
    }

    @Override
    public boolean tryLock(String lockKey, String value, long timeout, TimeUnit unit) {
        try {
            Boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, value, timeout, unit);
            log.debug("Redis尝试获取锁完成: lockKey={}, value={}, timeout={}, unit={}, success={}", 
                    lockKey, value, timeout, unit, success);
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            log.error("Redis尝试获取锁失败: lockKey={}, value={}, timeout={}, unit={}, error={}", 
                    lockKey, value, timeout, unit, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean releaseLock(String lockKey, String value) {
        try {
            Long result = redisTemplate.execute(releaseLockScript, Collections.singletonList(lockKey), value);
            boolean success = result != null && result > 0;
            log.debug("Redis释放锁完成: lockKey={}, value={}, success={}", lockKey, value, success);
            return success;
        } catch (Exception e) {
            log.error("Redis释放锁失败: lockKey={}, value={}, error={}", 
                    lockKey, value, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public <T> T executeWithLock(String lockKey, String value, long timeout, TimeUnit unit, Supplier<T> supplier) {
        return executeWithLock(lockKey, value, timeout, unit, supplier, 0, 0);
    }

    @Override
    public <T> T executeWithLock(String lockKey, String value, long timeout, TimeUnit unit, 
                              Supplier<T> supplier, int retryTimes, long retryInterval) {
        boolean locked = false;
        try {
            locked = tryLock(lockKey, value, timeout, unit);
            
            // 如果获取锁失败，并且设置了重试，则进行重试
            int retryCount = 0;
            while (!locked && retryCount < retryTimes) {
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("获取锁的重试过程被中断", e);
                }
                locked = tryLock(lockKey, value, timeout, unit);
                retryCount++;
            }
            
            if (locked) {
                log.debug("Redis获取锁成功，执行操作: lockKey={}, value={}", lockKey, value);
                return supplier.get();
            } else {
                log.warn("Redis获取锁失败，无法执行操作: lockKey={}, value={}, retries={}", 
                        lockKey, value, retryCount);
                throw new RuntimeException("获取锁失败，无法执行操作: " + lockKey);
            }
        } finally {
            if (locked) {
                releaseLock(lockKey, value);
            }
        }
    }

    @Override
    public boolean isLocked(String lockKey) {
        try {
            Boolean exists = redisTemplate.hasKey(lockKey);
            log.debug("Redis检查锁是否存在完成: lockKey={}, exists={}", lockKey, exists);
            return Boolean.TRUE.equals(exists);
        } catch (Exception e) {
            log.error("Redis检查锁是否存在失败: lockKey={}, error={}", lockKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean forceReleaseLock(String lockKey) {
        try {
            Boolean result = redisTemplate.delete(lockKey);
            log.debug("Redis强制释放锁完成: lockKey={}, result={}", lockKey, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis强制释放锁失败: lockKey={}, error={}", lockKey, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 生成唯一的锁值
     * 
     * @return 锁值
     */
    public static String generateLockValue() {
        return UUID.randomUUID().toString();
    }
} 