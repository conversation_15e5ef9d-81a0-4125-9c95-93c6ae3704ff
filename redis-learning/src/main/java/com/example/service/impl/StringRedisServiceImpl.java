package com.example.service.impl;

import com.example.service.StringRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Redis字符串操作服务实现类
 */
@Slf4j
@Service
public class StringRedisServiceImpl implements StringRedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    public StringRedisServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("Redis设置键值对成功: key={}", key);
        } catch (Exception e) {
            log.error("Redis设置键值对失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("Redis设置键值对及过期时间成功: key={}, timeout={}, unit={}", key, timeout, unit);
        } catch (Exception e) {
            log.error("Redis设置键值对及过期时间失败: key={}, timeout={}, unit={}, error={}", 
                    key, timeout, unit, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("Redis获取值成功: key={}", key);
            return value;
        } catch (Exception e) {
            log.error("Redis获取值失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.debug("Redis删除键成功: key={}, result={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis删除键失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean hasKey(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            log.debug("Redis检查键是否存在成功: key={}, result={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis检查键是否存在失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            log.debug("Redis设置过期时间成功: key={}, timeout={}, unit={}, result={}", 
                    key, timeout, unit, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis设置过期时间失败: key={}, timeout={}, unit={}, error={}", 
                    key, timeout, unit, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public long getExpire(String key, TimeUnit unit) {
        try {
            Long expire = redisTemplate.getExpire(key, unit);
            log.debug("Redis获取过期时间成功: key={}, unit={}, expire={}", key, unit, expire);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            log.error("Redis获取过期时间失败: key={}, unit={}, error={}", 
                    key, unit, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            log.debug("Redis增加值成功: key={}, delta={}, result={}", key, delta, result);
            return result;
        } catch (Exception e) {
            log.error("Redis增加值失败: key={}, delta={}, error={}", 
                    key, delta, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            log.debug("Redis减少值成功: key={}, delta={}, result={}", key, delta, result);
            return result;
        } catch (Exception e) {
            log.error("Redis减少值失败: key={}, delta={}, error={}", 
                    key, delta, e.getMessage(), e);
            throw e;
        }
    }
} 