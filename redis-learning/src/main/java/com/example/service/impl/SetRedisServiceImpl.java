package com.example.service.impl;

import com.example.service.SetRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Redis集合操作服务实现类
 */
@Slf4j
@Service
public class SetRedisServiceImpl implements SetRedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    public SetRedisServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Long add(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            log.debug("Redis添加元素到集合成功: key={}, count={}", key, count);
            return count;
        } catch (Exception e) {
            log.error("Redis添加元素到集合失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> members(String key) {
        try {
            Set<Object> members = redisTemplate.opsForSet().members(key);
            log.debug("Redis获取集合中的所有元素成功: key={}, size={}", 
                    key, members != null ? members.size() : 0);
            return members;
        } catch (Exception e) {
            log.error("Redis获取集合中的所有元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean isMember(String key, Object value) {
        try {
            Boolean result = redisTemplate.opsForSet().isMember(key, value);
            log.debug("Redis判断元素是否是集合的成员成功: key={}, result={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("Redis判断元素是否是集合的成员失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long size(String key) {
        try {
            Long size = redisTemplate.opsForSet().size(key);
            log.debug("Redis获取集合的大小成功: key={}, size={}", key, size);
            return size;
        } catch (Exception e) {
            log.error("Redis获取集合的大小失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long remove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            log.debug("Redis从集合中移除指定元素成功: key={}, count={}", key, count);
            return count;
        } catch (Exception e) {
            log.error("Redis从集合中移除指定元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object randomMember(String key) {
        try {
            Object member = redisTemplate.opsForSet().randomMember(key);
            log.debug("Redis随机获取集合中的一个元素成功: key={}", key);
            return member;
        } catch (Exception e) {
            log.error("Redis随机获取集合中的一个元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Object> randomMembers(String key, long count) {
        try {
            List<Object> members = redisTemplate.opsForSet().distinctRandomMembers(key, count);
            log.debug("Redis随机获取集合中的多个元素成功: key={}, count={}, size={}", 
                    key, count, members != null ? members.size() : 0);
            return members;
        } catch (Exception e) {
            log.error("Redis随机获取集合中的多个元素失败: key={}, count={}, error={}", 
                    key, count, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object pop(String key) {
        try {
            Object value = redisTemplate.opsForSet().pop(key);
            log.debug("Redis随机移除集合中的一个元素并返回成功: key={}", key);
            return value;
        } catch (Exception e) {
            log.error("Redis随机移除集合中的一个元素并返回失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> pop(String key, long count) {
        try {
            Set<Object> values = redisTemplate.opsForSet().pop(key, count);
            log.debug("Redis随机移除集合中的多个元素并返回成功: key={}, count={}, size={}", 
                    key, count, values != null ? values.size() : 0);
            return values;
        } catch (Exception e) {
            log.error("Redis随机移除集合中的多个元素并返回失败: key={}, count={}, error={}", 
                    key, count, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> intersect(String key, String otherKey) {
        try {
            Set<Object> result = redisTemplate.opsForSet().intersect(key, otherKey);
            log.debug("Redis计算两个集合的交集成功: key={}, otherKey={}, size={}", 
                    key, otherKey, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis计算两个集合的交集失败: key={}, otherKey={}, error={}", 
                    key, otherKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> intersect(String key, Set<String> otherKeys) {
        try {
            Set<Object> result = redisTemplate.opsForSet().intersect(key, otherKeys);
            log.debug("Redis计算多个集合的交集成功: key={}, otherKeys.size={}, size={}", 
                    key, otherKeys.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis计算多个集合的交集失败: key={}, otherKeys.size={}, error={}", 
                    key, otherKeys.size(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> union(String key, String otherKey) {
        try {
            Set<Object> result = redisTemplate.opsForSet().union(key, otherKey);
            log.debug("Redis计算两个集合的并集成功: key={}, otherKey={}, size={}", 
                    key, otherKey, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis计算两个集合的并集失败: key={}, otherKey={}, error={}", 
                    key, otherKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> union(String key, Set<String> otherKeys) {
        try {
            Set<Object> result = redisTemplate.opsForSet().union(key, otherKeys);
            log.debug("Redis计算多个集合的并集成功: key={}, otherKeys.size={}, size={}", 
                    key, otherKeys.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis计算多个集合的并集失败: key={}, otherKeys.size={}, error={}", 
                    key, otherKeys.size(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> difference(String key, String otherKey) {
        try {
            Set<Object> result = redisTemplate.opsForSet().difference(key, otherKey);
            log.debug("Redis计算两个集合的差集成功: key={}, otherKey={}, size={}", 
                    key, otherKey, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis计算两个集合的差集失败: key={}, otherKey={}, error={}", 
                    key, otherKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> difference(String key, Set<String> otherKeys) {
        try {
            Set<Object> result = redisTemplate.opsForSet().difference(key, otherKeys);
            log.debug("Redis计算多个集合的差集成功: key={}, otherKeys.size={}, size={}", 
                    key, otherKeys.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis计算多个集合的差集失败: key={}, otherKeys.size={}, error={}", 
                    key, otherKeys.size(), e.getMessage(), e);
            throw e;
        }
    }
} 