package com.example.service.impl;

import com.example.service.RedisTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Redis事务操作服务实现类
 */
@Slf4j
@Service
public class RedisTransactionServiceImpl implements RedisTransactionService {

    private final RedisTemplate<String, Object> redisTemplate;

    public RedisTransactionServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public <T> T executeInTransaction(SessionCallback<T> callback) {
        try {
            T result = redisTemplate.execute(callback);
            log.debug("Redis在事务中执行多个操作成功");
            return result;
        } catch (Exception e) {
            log.error("Redis在事务中执行多个操作失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void watch(String... keys) {
        try {
            for (String key : keys) {
                redisTemplate.watch(key);
            }
            log.debug("Redis监视键成功: keys={}", (Object) keys);
        } catch (Exception e) {
            log.error("Redis监视键失败: keys={}, error={}", (Object) keys, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void unwatch() {
        try {
            redisTemplate.unwatch();
            log.debug("Redis取消对所有键的监视成功");
        } catch (Exception e) {
            log.error("Redis取消对所有键的监视失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Object> simpleTransaction(Object... keysAndValues) {
        try {
            if (keysAndValues.length % 2 != 0) {
                throw new IllegalArgumentException("键值对参数数量必须是偶数");
            }

            List<Object> result = redisTemplate.execute(new SessionCallback<List<Object>>() {
                @Override
                public <K, V> List<Object> execute(RedisOperations<K, V> operations) throws DataAccessException {
                    operations.multi();

                    for (int i = 0; i < keysAndValues.length; i += 2) {
                        @SuppressWarnings("unchecked")
                        K key = (K) keysAndValues[i].toString();
                        @SuppressWarnings("unchecked")
                        V value = (V) keysAndValues[i + 1];
                        operations.opsForValue().set(key, value);
                    }

                    return operations.exec();
                }
            });

            log.debug("Redis执行简单事务成功: keysCount={}, result={}", 
                    keysAndValues.length / 2, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis执行简单事务失败: keysCount={}, error={}", 
                    keysAndValues.length / 2, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean conditionalTransaction(String key, Object expectedValue, Object newValue) {
        try {
            List<Object> result = redisTemplate.execute(new SessionCallback<List<Object>>() {
                @Override
                public <K, V> List<Object> execute(RedisOperations<K, V> operations) throws DataAccessException {
                    @SuppressWarnings("unchecked")
                    K k = (K) key;
                    
                    // 监视key
                    operations.watch(k);
                    
                    // 获取当前值
                    V currentValue = operations.opsForValue().get(k);
                    
                    // 如果当前值等于期望的值，则设置新值
                    if (Objects.equals(currentValue, expectedValue)) {
                        operations.multi();
                        @SuppressWarnings("unchecked")
                        V v = (V) newValue;
                        operations.opsForValue().set(k, v);
                        return operations.exec();
                    } else {
                        // 不等于期望值，放弃事务
                        operations.unwatch();
                        return null;
                    }
                }
            });
            
            boolean success = (result != null && !result.isEmpty());
            log.debug("Redis带条件执行的事务完成: key={}, success={}", key, success);
            return success;
        } catch (Exception e) {
            log.error("Redis带条件执行的事务失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }
} 