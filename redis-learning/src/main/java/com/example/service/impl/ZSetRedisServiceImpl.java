package com.example.service.impl;

import com.example.service.ZSetRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * Redis有序集合操作服务实现类
 */
@Slf4j
@Service
public class ZSetRedisServiceImpl implements ZSetRedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    public ZSetRedisServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Boolean add(String key, Object value, double score) {
        try {
            Boolean result = redisTemplate.opsForZSet().add(key, value, score);
            log.debug("Redis添加元素到有序集合成功: key={}, score={}, result={}", key, score, result);
            return result;
        } catch (Exception e) {
            log.error("Redis添加元素到有序集合失败: key={}, score={}, error={}", 
                    key, score, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long remove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForZSet().remove(key, values);
            log.debug("Redis从有序集合中移除元素成功: key={}, count={}", key, count);
            return count;
        } catch (Exception e) {
            log.error("Redis从有序集合中移除元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Double incrementScore(String key, Object value, double delta) {
        try {
            Double score = redisTemplate.opsForZSet().incrementScore(key, value, delta);
            log.debug("Redis增加元素的分数成功: key={}, delta={}, newScore={}", key, delta, score);
            return score;
        } catch (Exception e) {
            log.error("Redis增加元素的分数失败: key={}, delta={}, error={}", 
                    key, delta, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Double score(String key, Object value) {
        try {
            Double score = redisTemplate.opsForZSet().score(key, value);
            log.debug("Redis获取元素的分数成功: key={}, score={}", key, score);
            return score;
        } catch (Exception e) {
            log.error("Redis获取元素的分数失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> range(String key, long start, long end) {
        try {
            Set<Object> result = redisTemplate.opsForZSet().range(key, start, end);
            log.debug("Redis获取指定范围的元素成功: key={}, start={}, end={}, size={}", 
                    key, start, end, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis获取指定范围的元素失败: key={}, start={}, end={}, error={}", 
                    key, start, end, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> rangeByScore(String key, double min, double max) {
        try {
            Set<Object> result = redisTemplate.opsForZSet().rangeByScore(key, min, max);
            log.debug("Redis获取指定分数范围的元素成功: key={}, min={}, max={}, size={}", 
                    key, min, max, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis获取指定分数范围的元素失败: key={}, min={}, max={}, error={}", 
                    key, min, max, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<TypedTuple<Object>> rangeWithScores(String key, long start, long end) {
        try {
            Set<TypedTuple<Object>> result = redisTemplate.opsForZSet().rangeWithScores(key, start, end);
            log.debug("Redis获取指定范围的元素及其分数成功: key={}, start={}, end={}, size={}", 
                    key, start, end, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis获取指定范围的元素及其分数失败: key={}, start={}, end={}, error={}", 
                    key, start, end, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<TypedTuple<Object>> rangeByScoreWithScores(String key, double min, double max) {
        try {
            Set<TypedTuple<Object>> result = redisTemplate.opsForZSet().rangeByScoreWithScores(key, min, max);
            log.debug("Redis获取指定分数范围的元素及其分数成功: key={}, min={}, max={}, size={}", 
                    key, min, max, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis获取指定分数范围的元素及其分数失败: key={}, min={}, max={}, error={}", 
                    key, min, max, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long rank(String key, Object value) {
        try {
            Long rank = redisTemplate.opsForZSet().rank(key, value);
            log.debug("Redis获取元素的排名成功: key={}, rank={}", key, rank);
            return rank;
        } catch (Exception e) {
            log.error("Redis获取元素的排名失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long reverseRank(String key, Object value) {
        try {
            Long rank = redisTemplate.opsForZSet().reverseRank(key, value);
            log.debug("Redis获取元素的逆序排名成功: key={}, rank={}", key, rank);
            return rank;
        } catch (Exception e) {
            log.error("Redis获取元素的逆序排名失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long size(String key) {
        try {
            Long size = redisTemplate.opsForZSet().size(key);
            log.debug("Redis获取有序集合的大小成功: key={}, size={}", key, size);
            return size;
        } catch (Exception e) {
            log.error("Redis获取有序集合的大小失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long count(String key, double min, double max) {
        try {
            Long count = redisTemplate.opsForZSet().count(key, min, max);
            log.debug("Redis获取指定分数范围内的元素数量成功: key={}, min={}, max={}, count={}", 
                    key, min, max, count);
            return count;
        } catch (Exception e) {
            log.error("Redis获取指定分数范围内的元素数量失败: key={}, min={}, max={}, error={}", 
                    key, min, max, e.getMessage(), e);
            throw e;
        }
    }
}