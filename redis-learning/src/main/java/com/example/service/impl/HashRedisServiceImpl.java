package com.example.service.impl;

import com.example.service.HashRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Redis哈希操作服务实现类
 */
@Slf4j
@Service
public class HashRedisServiceImpl implements HashRedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    public HashRedisServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void put(String key, Object hashKey, Object value) {
        try {
            redisTemplate.opsForHash().put(key, hashKey, value);
            log.debug("Redis设置哈希表字段值成功: key={}, hashKey={}", key, hashKey);
        } catch (Exception e) {
            log.error("Redis设置哈希表字段值失败: key={}, hashKey={}, error={}", 
                    key, hashKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void putAll(String key, Map<Object, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            log.debug("Redis批量设置哈希表字段值成功: key={}, count={}", 
                    key, map != null ? map.size() : 0);
        } catch (Exception e) {
            log.error("Redis批量设置哈希表字段值失败: key={}, count={}, error={}", 
                    key, map != null ? map.size() : 0, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean putIfAbsent(String key, Object hashKey, Object value) {
        try {
            Boolean result = redisTemplate.opsForHash().putIfAbsent(key, hashKey, value);
            log.debug("Redis仅当hashKey不存在时设置哈希表字段值成功: key={}, hashKey={}, result={}", 
                    key, hashKey, result);
            return result;
        } catch (Exception e) {
            log.error("Redis仅当hashKey不存在时设置哈希表字段值失败: key={}, hashKey={}, error={}", 
                    key, hashKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object get(String key, Object hashKey) {
        try {
            Object value = redisTemplate.opsForHash().get(key, hashKey);
            log.debug("Redis获取哈希表字段值成功: key={}, hashKey={}", key, hashKey);
            return value;
        } catch (Exception e) {
            log.error("Redis获取哈希表字段值失败: key={}, hashKey={}, error={}", 
                    key, hashKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Object> multiGet(String key, List<Object> hashKeys) {
        try {
            List<Object> values = redisTemplate.opsForHash().multiGet(key, hashKeys);
            log.debug("Redis批量获取哈希表字段值成功: key={}, count={}, result.size={}", 
                    key, hashKeys != null ? hashKeys.size() : 0, values != null ? values.size() : 0);
            return values;
        } catch (Exception e) {
            log.error("Redis批量获取哈希表字段值失败: key={}, count={}, error={}", 
                    key, hashKeys != null ? hashKeys.size() : 0, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long delete(String key, Object... hashKeys) {
        try {
            Long count = redisTemplate.opsForHash().delete(key, hashKeys);
            log.debug("Redis删除哈希表字段成功: key={}, count={}", key, count);
            return count;
        } catch (Exception e) {
            log.error("Redis删除哈希表字段失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean hasKey(String key, Object hashKey) {
        try {
            Boolean result = redisTemplate.opsForHash().hasKey(key, hashKey);
            log.debug("Redis判断哈希表字段是否存在成功: key={}, hashKey={}, result={}", 
                    key, hashKey, result);
            return result;
        } catch (Exception e) {
            log.error("Redis判断哈希表字段是否存在失败: key={}, hashKey={}, error={}", 
                    key, hashKey, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Set<Object> keys(String key) {
        try {
            Set<Object> keys = redisTemplate.opsForHash().keys(key);
            log.debug("Redis获取哈希表所有字段成功: key={}, size={}", 
                    key, keys != null ? keys.size() : 0);
            return keys;
        } catch (Exception e) {
            log.error("Redis获取哈希表所有字段失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Object> values(String key) {
        try {
            List<Object> values = redisTemplate.opsForHash().values(key);
            log.debug("Redis获取哈希表所有值成功: key={}, size={}", 
                    key, values != null ? values.size() : 0);
            return values;
        } catch (Exception e) {
            log.error("Redis获取哈希表所有值失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Map<Object, Object> entries(String key) {
        try {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            log.debug("Redis获取哈希表所有字段和值成功: key={}, size={}", 
                    key, entries != null ? entries.size() : 0);
            return entries;
        } catch (Exception e) {
            log.error("Redis获取哈希表所有字段和值失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long size(String key) {
        try {
            Long size = redisTemplate.opsForHash().size(key);
            log.debug("Redis获取哈希表大小成功: key={}, size={}", key, size);
            return size;
        } catch (Exception e) {
            log.error("Redis获取哈希表大小失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long increment(String key, Object hashKey, long delta) {
        try {
            Long value = redisTemplate.opsForHash().increment(key, hashKey, delta);
            log.debug("Redis增加哈希表字段值成功: key={}, hashKey={}, delta={}, value={}", 
                    key, hashKey, delta, value);
            return value;
        } catch (Exception e) {
            log.error("Redis增加哈希表字段值失败: key={}, hashKey={}, delta={}, error={}", 
                    key, hashKey, delta, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Double increment(String key, Object hashKey, double delta) {
        try {
            Double value = redisTemplate.opsForHash().increment(key, hashKey, delta);
            log.debug("Redis增加哈希表字段值（浮点数）成功: key={}, hashKey={}, delta={}, value={}", 
                    key, hashKey, delta, value);
            return value;
        } catch (Exception e) {
            log.error("Redis增加哈希表字段值（浮点数）失败: key={}, hashKey={}, delta={}, error={}", 
                    key, hashKey, delta, e.getMessage(), e);
            throw e;
        }
    }
} 