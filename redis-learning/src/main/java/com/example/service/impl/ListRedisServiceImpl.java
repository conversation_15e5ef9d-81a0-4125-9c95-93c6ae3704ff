package com.example.service.impl;

import com.example.service.ListRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Redis列表操作服务实现类
 */
@Slf4j
@Service
public class ListRedisServiceImpl implements ListRedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    public ListRedisServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Long leftPush(String key, Object value) {
        try {
            Long result = redisTemplate.opsForList().leftPush(key, value);
            log.debug("Redis从列表左侧添加元素成功: key={}, result={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表左侧添加元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long leftPushAll(String key, Object... values) {
        try {
            Long result = redisTemplate.opsForList().leftPushAll(key, values);
            log.debug("Redis从列表左侧批量添加元素成功: key={}, count={}, result={}", 
                    key, values.length, result);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表左侧批量添加元素失败: key={}, count={}, error={}", 
                    key, values.length, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long rightPush(String key, Object value) {
        try {
            Long result = redisTemplate.opsForList().rightPush(key, value);
            log.debug("Redis从列表右侧添加元素成功: key={}, result={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表右侧添加元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long rightPushAll(String key, Object... values) {
        try {
            Long result = redisTemplate.opsForList().rightPushAll(key, values);
            log.debug("Redis从列表右侧批量添加元素成功: key={}, count={}, result={}", 
                    key, values.length, result);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表右侧批量添加元素失败: key={}, count={}, error={}", 
                    key, values.length, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object leftPop(String key) {
        try {
            Object result = redisTemplate.opsForList().leftPop(key);
            log.debug("Redis从列表左侧弹出元素成功: key={}", key);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表左侧弹出元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object rightPop(String key) {
        try {
            Object result = redisTemplate.opsForList().rightPop(key);
            log.debug("Redis从列表右侧弹出元素成功: key={}", key);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表右侧弹出元素失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Object> range(String key, long start, long end) {
        try {
            List<Object> result = redisTemplate.opsForList().range(key, start, end);
            log.debug("Redis获取列表指定范围内的元素成功: key={}, start={}, end={}, size={}", 
                    key, start, end, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("Redis获取列表指定范围内的元素失败: key={}, start={}, end={}, error={}", 
                    key, start, end, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long size(String key) {
        try {
            Long size = redisTemplate.opsForList().size(key);
            log.debug("Redis获取列表长度成功: key={}, size={}", key, size);
            return size;
        } catch (Exception e) {
            log.error("Redis获取列表长度失败: key={}, error={}", key, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Object index(String key, long index) {
        try {
            Object result = redisTemplate.opsForList().index(key, index);
            log.debug("Redis通过索引获取列表中的元素成功: key={}, index={}", key, index);
            return result;
        } catch (Exception e) {
            log.error("Redis通过索引获取列表中的元素失败: key={}, index={}, error={}", 
                    key, index, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void set(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            log.debug("Redis在列表的指定位置设置元素成功: key={}, index={}", key, index);
        } catch (Exception e) {
            log.error("Redis在列表的指定位置设置元素失败: key={}, index={}, error={}", 
                    key, index, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Long remove(String key, long count, Object value) {
        try {
            Long result = redisTemplate.opsForList().remove(key, count, value);
            log.debug("Redis从列表中删除指定值的元素成功: key={}, count={}, result={}", 
                    key, count, result);
            return result;
        } catch (Exception e) {
            log.error("Redis从列表中删除指定值的元素失败: key={}, count={}, error={}", 
                    key, count, e.getMessage(), e);
            throw e;
        }
    }
} 