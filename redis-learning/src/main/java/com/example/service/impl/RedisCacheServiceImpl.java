package com.example.service.impl;

import com.example.service.RedisCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis缓存服务实现类
 */
@Slf4j
@Service
public class RedisCacheServiceImpl implements RedisCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private static final long DEFAULT_TIMEOUT = 1800; // 默认30分钟
    private static final TimeUnit DEFAULT_TIMEUNIT = TimeUnit.SECONDS;
    private static final long LOCK_TIMEOUT = 10; // 锁超时10秒
    private static final TimeUnit LOCK_TIMEUNIT = TimeUnit.SECONDS;
    private static final long LOCK_WAIT_TIMEOUT = 50; // 等待锁的最大时间50毫秒
    private static final TimeUnit LOCK_WAIT_TIMEUNIT = TimeUnit.MILLISECONDS;

    public RedisCacheServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public <T> T getWithCache(String key, Supplier<T> dataLoader) {
        return getWithCache(key, dataLoader, DEFAULT_TIMEOUT, DEFAULT_TIMEUNIT);
    }

    @Override
    public <T> T getWithCache(String key, Supplier<T> dataLoader, long timeout, TimeUnit unit) {
        try {
            @SuppressWarnings("unchecked")
            T value = (T) redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("Redis从缓存获取数据成功: key={}", key);
                return value;
            }
            
            // 从数据加载器获取数据
            value = dataLoader.get();
            
            if (value != null) {
                // 将数据存入缓存
                redisTemplate.opsForValue().set(key, value, timeout, unit);
                log.debug("Redis将数据加载到缓存成功: key={}, timeout={}, unit={}", key, timeout, unit);
            }
            
            return value;
        } catch (Exception e) {
            log.error("Redis缓存操作失败: key={}, error={}", key, e.getMessage(), e);
            // 当缓存失败时，从数据加载器获取数据，但不存入缓存
            return dataLoader.get();
        }
    }

    @Override
    public <T> boolean putCache(String key, T value) {
        return putCache(key, value, DEFAULT_TIMEOUT, DEFAULT_TIMEUNIT);
    }

    @Override
    public <T> boolean putCache(String key, T value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("Redis将数据放入缓存成功: key={}, timeout={}, unit={}", key, timeout, unit);
            return true;
        } catch (Exception e) {
            log.error("Redis将数据放入缓存失败: key={}, timeout={}, unit={}, error={}", 
                    key, timeout, unit, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getFromCache(String key) {
        try {
            T value = (T) redisTemplate.opsForValue().get(key);
            log.debug("Redis从缓存获取数据完成: key={}, exists={}", key, value != null);
            return value;
        } catch (Exception e) {
            log.error("Redis从缓存获取数据失败: key={}, error={}", key, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean removeCache(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.debug("Redis从缓存删除数据完成: key={}, result={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis从缓存删除数据失败: key={}, error={}", key, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean hasKey(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            log.debug("Redis检查缓存中是否存在指定键完成: key={}, result={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis检查缓存中是否存在指定键失败: key={}, error={}", key, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public <T> T getWithLock(String key, String lockKey, Supplier<T> dataLoader, long timeout, TimeUnit unit) {
        try {
            @SuppressWarnings("unchecked")
            T value = (T) redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                log.debug("Redis从缓存获取数据成功: key={}", key);
                return value;
            }
            
            // 尝试获取锁
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", LOCK_TIMEOUT, LOCK_TIMEUNIT);
            
            if (Boolean.TRUE.equals(lockAcquired)) {
                try {
                    // 再次检查缓存，防止在获取锁的过程中，其他线程已经加载了数据
                    @SuppressWarnings("unchecked")
                    T checkAgain = (T) redisTemplate.opsForValue().get(key);
                    
                    if (checkAgain != null) {
                        log.debug("Redis获取锁后再次从缓存获取数据成功: key={}", key);
                        return checkAgain;
                    }
                    
                    // 从数据加载器获取数据
                    value = dataLoader.get();
                    
                    if (value != null) {
                        // 将数据存入缓存
                        redisTemplate.opsForValue().set(key, value, timeout, unit);
                        log.debug("Redis使用锁将数据加载到缓存成功: key={}, timeout={}, unit={}", key, timeout, unit);
                    }
                    
                    return value;
                } finally {
                    // 释放锁
                    redisTemplate.delete(lockKey);
                    log.debug("Redis释放缓存锁: lockKey={}", lockKey);
                }
            } else {
                // 没有获取到锁，等待一段时间后再尝试从缓存获取数据
                LOCK_WAIT_TIMEUNIT.sleep(LOCK_WAIT_TIMEOUT);
                @SuppressWarnings("unchecked")
                T waitValue = (T) redisTemplate.opsForValue().get(key);
                
                if (waitValue != null) {
                    log.debug("Redis等待锁释放后从缓存获取数据成功: key={}", key);
                    return waitValue;
                }
                
                // 如果仍然没有数据，则直接从数据加载器获取
                log.debug("Redis等待锁释放后仍未获取到缓存数据，直接从数据源加载: key={}", key);
                return dataLoader.get();
            }
        } catch (Exception e) {
            log.error("Redis带锁的缓存操作失败: key={}, lockKey={}, error={}", 
                    key, lockKey, e.getMessage(), e);
            // 当缓存失败时，从数据加载器获取数据，但不存入缓存
            return dataLoader.get();
        }
    }
}