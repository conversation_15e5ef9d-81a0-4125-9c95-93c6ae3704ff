package com.example.service;

import java.util.List;
import java.util.Set;

/**
 * Redis集合操作服务接口
 */
public interface SetRedisService {
    
    /**
     * 添加元素到集合
     * 
     * @param key 键
     * @param values 值集合
     * @return 成功添加的数量
     */
    Long add(String key, Object... values);
    
    /**
     * 获取集合中的所有元素
     * 
     * @param key 键
     * @return 元素集合
     */
    Set<Object> members(String key);
    
    /**
     * 判断元素是否是集合的成员
     * 
     * @param key 键
     * @param value 值
     * @return 是否是成员
     */
    Boolean isMember(String key, Object value);
    
    /**
     * 获取集合的大小
     * 
     * @param key 键
     * @return 集合大小
     */
    Long size(String key);
    
    /**
     * 从集合中移除指定元素
     * 
     * @param key 键
     * @param values 值集合
     * @return 成功移除的数量
     */
    Long remove(String key, Object... values);
    
    /**
     * 随机获取集合中的一个元素
     * 
     * @param key 键
     * @return 随机元素
     */
    Object randomMember(String key);
    
    /**
     * 随机获取集合中的多个元素
     * 
     * @param key 键
     * @param count 数量
     * @return 随机元素列表
     */
    List<Object> randomMembers(String key, long count);
    
    /**
     * 随机移除集合中的一个元素并返回
     * 
     * @param key 键
     * @return 移除的元素
     */
    Object pop(String key);
    
    /**
     * 随机移除集合中的多个元素并返回
     * 
     * @param key 键
     * @param count 数量
     * @return 移除的元素集合
     */
    Set<Object> pop(String key, long count);
    
    /**
     * 计算两个集合的交集
     * 
     * @param key 键
     * @param otherKey 其他键
     * @return 交集元素集合
     */
    Set<Object> intersect(String key, String otherKey);
    
    /**
     * 计算多个集合的交集
     * 
     * @param key 键
     * @param otherKeys 其他键集合
     * @return 交集元素集合
     */
    Set<Object> intersect(String key, Set<String> otherKeys);
    
    /**
     * 计算两个集合的并集
     * 
     * @param key 键
     * @param otherKey 其他键
     * @return 并集元素集合
     */
    Set<Object> union(String key, String otherKey);
    
    /**
     * 计算多个集合的并集
     * 
     * @param key 键
     * @param otherKeys 其他键集合
     * @return 并集元素集合
     */
    Set<Object> union(String key, Set<String> otherKeys);
    
    /**
     * 计算两个集合的差集
     * 
     * @param key 键
     * @param otherKey 其他键
     * @return 差集元素集合
     */
    Set<Object> difference(String key, String otherKey);
    
    /**
     * 计算多个集合的差集
     * 
     * @param key 键
     * @param otherKeys 其他键集合
     * @return 差集元素集合
     */
    Set<Object> difference(String key, Set<String> otherKeys);
} 