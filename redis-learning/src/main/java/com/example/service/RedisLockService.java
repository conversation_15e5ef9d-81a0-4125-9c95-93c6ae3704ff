package com.example.service;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis分布式锁服务接口
 */
public interface RedisLockService {
    
    /**
     * 尝试获取锁
     * 
     * @param lockKey 锁键
     * @param value 锁值（标识锁的持有者）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取锁
     */
    boolean tryLock(String lockKey, String value, long timeout, TimeUnit unit);
    
    /**
     * 释放锁
     * 
     * @param lockKey 锁键
     * @param value 锁值（标识锁的持有者）
     * @return 是否成功释放锁
     */
    boolean releaseLock(String lockKey, String value);
    
    /**
     * 使用锁执行操作
     * 
     * @param lockKey 锁键
     * @param value 锁值（标识锁的持有者）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @param supplier 操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    <T> T executeWithLock(String lockKey, String value, long timeout, TimeUnit unit, Supplier<T> supplier);
    
    /**
     * 使用锁执行操作，可指定获取锁的重试次数和重试间隔
     * 
     * @param lockKey 锁键
     * @param value 锁值（标识锁的持有者）
     * @param timeout 超时时间
     * @param unit 时间单位
     * @param supplier 操作
     * @param retryTimes 重试次数
     * @param retryInterval 重试间隔（毫秒）
     * @param <T> 返回类型
     * @return 操作结果
     */
    <T> T executeWithLock(String lockKey, String value, long timeout, TimeUnit unit, 
                          Supplier<T> supplier, int retryTimes, long retryInterval);
    
    /**
     * 检查锁是否存在
     * 
     * @param lockKey 锁键
     * @return 是否存在
     */
    boolean isLocked(String lockKey);
    
    /**
     * 强制释放锁（不检查锁的持有者）
     * 
     * @param lockKey 锁键
     * @return 是否成功释放锁
     */
    boolean forceReleaseLock(String lockKey);
} 