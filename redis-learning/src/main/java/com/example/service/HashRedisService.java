package com.example.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Redis哈希操作服务接口
 */
public interface HashRedisService {
    
    /**
     * 设置哈希表字段值
     * 
     * @param key 键
     * @param hashKey 哈希表字段
     * @param value 值
     */
    void put(String key, Object hashKey, Object value);
    
    /**
     * 批量设置哈希表字段值
     * 
     * @param key 键
     * @param map 哈希表字段-值映射
     */
    void putAll(String key, Map<Object, Object> map);
    
    /**
     * 仅当hashKey不存在时，才设置哈希表字段值
     * 
     * @param key 键
     * @param hashKey 哈希表字段
     * @param value 值
     * @return 是否成功
     */
    Boolean putIfAbsent(String key, Object hashKey, Object value);
    
    /**
     * 获取哈希表字段值
     * 
     * @param key 键
     * @param hashKey 哈希表字段
     * @return 值
     */
    Object get(String key, Object hashKey);
    
    /**
     * 批量获取哈希表字段值
     * 
     * @param key 键
     * @param hashKeys 哈希表字段集合
     * @return 值列表
     */
    List<Object> multiGet(String key, List<Object> hashKeys);
    
    /**
     * 删除哈希表字段
     * 
     * @param key 键
     * @param hashKeys 哈希表字段集合
     * @return 删除的数量
     */
    Long delete(String key, Object... hashKeys);
    
    /**
     * 判断哈希表字段是否存在
     * 
     * @param key 键
     * @param hashKey 哈希表字段
     * @return 是否存在
     */
    Boolean hasKey(String key, Object hashKey);
    
    /**
     * 获取哈希表所有字段
     * 
     * @param key 键
     * @return 字段集合
     */
    Set<Object> keys(String key);
    
    /**
     * 获取哈希表所有值
     * 
     * @param key 键
     * @return 值列表
     */
    List<Object> values(String key);
    
    /**
     * 获取哈希表所有字段和值
     * 
     * @param key 键
     * @return 字段-值映射
     */
    Map<Object, Object> entries(String key);
    
    /**
     * 获取哈希表大小
     * 
     * @param key 键
     * @return 大小
     */
    Long size(String key);
    
    /**
     * 增加哈希表字段值
     * 
     * @param key 键
     * @param hashKey 哈希表字段
     * @param delta 增量
     * @return 增加后的值
     */
    Long increment(String key, Object hashKey, long delta);
    
    /**
     * 增加哈希表字段值（浮点数）
     * 
     * @param key 键
     * @param hashKey 哈希表字段
     * @param delta 增量
     * @return 增加后的值
     */
    Double increment(String key, Object hashKey, double delta);
} 