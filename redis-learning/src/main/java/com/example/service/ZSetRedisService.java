package com.example.service;

import org.springframework.data.redis.core.ZSetOperations.TypedTuple;

import java.util.Set;

/**
 * Redis有序集合操作服务接口
 */
public interface ZSetRedisService {
    
    /**
     * 添加元素到有序集合
     * 
     * @param key 键
     * @param value 值
     * @param score 分数
     * @return 是否成功
     */
    Boolean add(String key, Object value, double score);
    
    /**
     * 从有序集合中移除元素
     * 
     * @param key 键
     * @param values 值集合
     * @return 移除的数量
     */
    Long remove(String key, Object... values);
    
    /**
     * 增加元素的分数
     * 
     * @param key 键
     * @param value 值
     * @param delta 增加的分数
     * @return 增加后的分数
     */
    Double incrementScore(String key, Object value, double delta);
    
    /**
     * 获取元素的分数
     * 
     * @param key 键
     * @param value 值
     * @return 分数
     */
    Double score(String key, Object value);
    
    /**
     * 获取指定范围的元素
     * 
     * @param key 键
     * @param start 开始索引
     * @param end 结束索引
     * @return 元素集合
     */
    Set<Object> range(String key, long start, long end);
    
    /**
     * 获取指定分数范围的元素
     * 
     * @param key 键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素集合
     */
    Set<Object> rangeByScore(String key, double min, double max);
    
    /**
     * 获取指定范围的元素及其分数
     * 
     * @param key 键
     * @param start 开始索引
     * @param end 结束索引
     * @return 元素及分数的集合
     */
    Set<TypedTuple<Object>> rangeWithScores(String key, long start, long end);
    
    /**
     * 获取指定分数范围的元素及其分数
     * 
     * @param key 键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素及分数的集合
     */
    Set<TypedTuple<Object>> rangeByScoreWithScores(String key, double min, double max);
    
    /**
     * 获取元素的排名
     * 
     * @param key 键
     * @param value 值
     * @return 排名（从0开始）
     */
    Long rank(String key, Object value);
    
    /**
     * 获取元素的逆序排名
     * 
     * @param key 键
     * @param value 值
     * @return 逆序排名（从0开始）
     */
    Long reverseRank(String key, Object value);
    
    /**
     * 获取有序集合的大小
     * 
     * @param key 键
     * @return 大小
     */
    Long size(String key);
    
    /**
     * 获取指定分数范围内的元素数量
     * 
     * @param key 键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素数量
     */
    Long count(String key, double min, double max);
} 